"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-slider@1.2._57f520f88d0738ee932bb8049d646b3d";
exports.ids = ["vendor-chunks/@radix-ui+react-slider@1.2._57f520f88d0738ee932bb8049d646b3d"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-slider@1.2._57f520f88d0738ee932bb8049d646b3d/node_modules/@radix-ui/react-slider/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-slider@1.2._57f520f88d0738ee932bb8049d646b3d/node_modules/@radix-ui/react-slider/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Range: () => (/* binding */ Range),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slider: () => (/* binding */ Slider),\n/* harmony export */   SliderRange: () => (/* binding */ SliderRange),\n/* harmony export */   SliderThumb: () => (/* binding */ SliderThumb),\n/* harmony export */   SliderTrack: () => (/* binding */ SliderTrack),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Track: () => (/* binding */ Track),\n/* harmony export */   createSliderScope: () => (/* binding */ createSliderScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_84b05e8d8acde331bf79519153011e46/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_033f2d9864c311ebc46e3d65a72ec4b7/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_f62eb26ebf07111fa37167228db23a06/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._25ea7b66547079fee23d7c838aabe8ed/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_5a9538d81cc9a82cfec21341eafe5f60/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Range,Root,Slider,SliderRange,SliderThumb,SliderTrack,Thumb,Track,createSliderScope auto */ // packages/react/slider/src/Slider.tsx\n\n\n\n\n\n\n\n\n\n\n\n\nvar PAGE_KEYS = [\n    \"PageUp\",\n    \"PageDown\"\n];\nvar ARROW_KEYS = [\n    \"ArrowUp\",\n    \"ArrowDown\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar BACK_KEYS = {\n    \"from-left\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowLeft\"\n    ],\n    \"from-right\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowRight\"\n    ],\n    \"from-bottom\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowDown\",\n        \"ArrowLeft\"\n    ],\n    \"from-top\": [\n        \"Home\",\n        \"PageDown\",\n        \"ArrowUp\",\n        \"ArrowLeft\"\n    ]\n};\nvar SLIDER_NAME = \"Slider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(SLIDER_NAME);\nvar [createSliderContext, createSliderScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(SLIDER_NAME, [\n    createCollectionScope\n]);\nvar [SliderProvider, useSliderContext] = createSliderContext(SLIDER_NAME);\nvar Slider = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { name, min = 0, max = 100, step = 1, orientation = \"horizontal\", disabled = false, minStepsBetweenThumbs = 0, defaultValue = [\n        min\n    ], value, onValueChange = ()=>{}, onValueCommit = ()=>{}, inverted = false, form, ...sliderProps } = props;\n    const thumbRefs = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Set());\n    const valueIndexToChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const isHorizontal = orientation === \"horizontal\";\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n    const [values = [], setValues] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: value,\n        defaultProp: defaultValue,\n        onChange: {\n            \"Slider.useControllableState\": (value2)=>{\n                const thumbs = [\n                    ...thumbRefs.current\n                ];\n                thumbs[valueIndexToChangeRef.current]?.focus();\n                onValueChange(value2);\n            }\n        }[\"Slider.useControllableState\"]\n    });\n    const valuesBeforeSlideStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(values);\n    function handleSlideStart(value2) {\n        const closestIndex = getClosestValueIndex(values, value2);\n        updateValues(value2, closestIndex);\n    }\n    function handleSlideMove(value2) {\n        updateValues(value2, valueIndexToChangeRef.current);\n    }\n    function handleSlideEnd() {\n        const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n        const nextValue = values[valueIndexToChangeRef.current];\n        const hasChanged = nextValue !== prevValue;\n        if (hasChanged) onValueCommit(values);\n    }\n    function updateValues(value2, atIndex, { commit } = {\n        commit: false\n    }) {\n        const decimalCount = getDecimalCount(step);\n        const snapToStep = roundValue(Math.round((value2 - min) / step) * step + min, decimalCount);\n        const nextValue = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_5__.clamp)(snapToStep, [\n            min,\n            max\n        ]);\n        setValues((prevValues = [])=>{\n            const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n            if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n                valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n                const hasChanged = String(nextValues) !== String(prevValues);\n                if (hasChanged && commit) onValueCommit(nextValues);\n                return hasChanged ? nextValues : prevValues;\n            } else {\n                return prevValues;\n            }\n        });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderProvider, {\n        scope: props.__scopeSlider,\n        name,\n        disabled,\n        min,\n        max,\n        valueIndexToChangeRef,\n        thumbs: thumbRefs.current,\n        values,\n        orientation,\n        form,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n            scope: props.__scopeSlider,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeSlider,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientation, {\n                    \"aria-disabled\": disabled,\n                    \"data-disabled\": disabled ? \"\" : void 0,\n                    ...sliderProps,\n                    ref: forwardedRef,\n                    onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(sliderProps.onPointerDown, ()=>{\n                        if (!disabled) valuesBeforeSlideStartRef.current = values;\n                    }),\n                    min,\n                    max,\n                    inverted,\n                    onSlideStart: disabled ? void 0 : handleSlideStart,\n                    onSlideMove: disabled ? void 0 : handleSlideMove,\n                    onSlideEnd: disabled ? void 0 : handleSlideEnd,\n                    onHomeKeyDown: ()=>!disabled && updateValues(min, 0, {\n                            commit: true\n                        }),\n                    onEndKeyDown: ()=>!disabled && updateValues(max, values.length - 1, {\n                            commit: true\n                        }),\n                    onStepKeyDown: ({ event, direction: stepDirection })=>{\n                        if (!disabled) {\n                            const isPageKey = PAGE_KEYS.includes(event.key);\n                            const isSkipKey = isPageKey || event.shiftKey && ARROW_KEYS.includes(event.key);\n                            const multiplier = isSkipKey ? 10 : 1;\n                            const atIndex = valueIndexToChangeRef.current;\n                            const value2 = values[atIndex];\n                            const stepInDirection = step * multiplier * stepDirection;\n                            updateValues(value2 + stepInDirection, atIndex, {\n                                commit: true\n                            });\n                        }\n                    }\n                })\n            })\n        })\n    });\n});\nSlider.displayName = SLIDER_NAME;\nvar [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext(SLIDER_NAME, {\n    startEdge: \"left\",\n    endEdge: \"right\",\n    size: \"width\",\n    direction: 1\n});\nvar SliderHorizontal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { min, max, dir, inverted, onSlideStart, onSlideMove, onSlideEnd, onStepKeyDown, ...sliderProps } = props;\n    const [slider, setSlider] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderHorizontal.useComposedRefs[composedRefs]\": (node)=>setSlider(node)\n    }[\"SliderHorizontal.useComposedRefs[composedRefs]\"]);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_8__.useDirection)(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const isSlidingFromLeft = isDirectionLTR && !inverted || !isDirectionLTR && inverted;\n    function getValueFromPointer(pointerPosition) {\n        const rect = rectRef.current || slider.getBoundingClientRect();\n        const input = [\n            0,\n            rect.width\n        ];\n        const output = isSlidingFromLeft ? [\n            min,\n            max\n        ] : [\n            max,\n            min\n        ];\n        const value = linearScale(input, output);\n        rectRef.current = rect;\n        return value(pointerPosition - rect.left);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientationProvider, {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromLeft ? \"left\" : \"right\",\n        endEdge: isSlidingFromLeft ? \"right\" : \"left\",\n        direction: isSlidingFromLeft ? 1 : -1,\n        size: \"width\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderImpl, {\n            dir: direction,\n            \"data-orientation\": \"horizontal\",\n            ...sliderProps,\n            ref: composedRefs,\n            style: {\n                ...sliderProps.style,\n                [\"--radix-slider-thumb-transform\"]: \"translateX(-50%)\"\n            },\n            onSlideStart: (event)=>{\n                const value = getValueFromPointer(event.clientX);\n                onSlideStart?.(value);\n            },\n            onSlideMove: (event)=>{\n                const value = getValueFromPointer(event.clientX);\n                onSlideMove?.(value);\n            },\n            onSlideEnd: ()=>{\n                rectRef.current = void 0;\n                onSlideEnd?.();\n            },\n            onStepKeyDown: (event)=>{\n                const slideDirection = isSlidingFromLeft ? \"from-left\" : \"from-right\";\n                const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n                onStepKeyDown?.({\n                    event,\n                    direction: isBackKey ? -1 : 1\n                });\n            }\n        })\n    });\n});\nvar SliderVertical = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { min, max, inverted, onSlideStart, onSlideMove, onSlideEnd, onStepKeyDown, ...sliderProps } = props;\n    const sliderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, sliderRef);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const isSlidingFromBottom = !inverted;\n    function getValueFromPointer(pointerPosition) {\n        const rect = rectRef.current || sliderRef.current.getBoundingClientRect();\n        const input = [\n            0,\n            rect.height\n        ];\n        const output = isSlidingFromBottom ? [\n            max,\n            min\n        ] : [\n            min,\n            max\n        ];\n        const value = linearScale(input, output);\n        rectRef.current = rect;\n        return value(pointerPosition - rect.top);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderOrientationProvider, {\n        scope: props.__scopeSlider,\n        startEdge: isSlidingFromBottom ? \"bottom\" : \"top\",\n        endEdge: isSlidingFromBottom ? \"top\" : \"bottom\",\n        size: \"height\",\n        direction: isSlidingFromBottom ? 1 : -1,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderImpl, {\n            \"data-orientation\": \"vertical\",\n            ...sliderProps,\n            ref,\n            style: {\n                ...sliderProps.style,\n                [\"--radix-slider-thumb-transform\"]: \"translateY(50%)\"\n            },\n            onSlideStart: (event)=>{\n                const value = getValueFromPointer(event.clientY);\n                onSlideStart?.(value);\n            },\n            onSlideMove: (event)=>{\n                const value = getValueFromPointer(event.clientY);\n                onSlideMove?.(value);\n            },\n            onSlideEnd: ()=>{\n                rectRef.current = void 0;\n                onSlideEnd?.();\n            },\n            onStepKeyDown: (event)=>{\n                const slideDirection = isSlidingFromBottom ? \"from-bottom\" : \"from-top\";\n                const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n                onStepKeyDown?.({\n                    event,\n                    direction: isBackKey ? -1 : 1\n                });\n            }\n        })\n    });\n});\nvar SliderImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, onSlideStart, onSlideMove, onSlideEnd, onHomeKeyDown, onEndKeyDown, onStepKeyDown, ...sliderProps } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        ...sliderProps,\n        ref: forwardedRef,\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            if (event.key === \"Home\") {\n                onHomeKeyDown(event);\n                event.preventDefault();\n            } else if (event.key === \"End\") {\n                onEndKeyDown(event);\n                event.preventDefault();\n            } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n                onStepKeyDown(event);\n                event.preventDefault();\n            }\n        }),\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerDown, (event)=>{\n            const target = event.target;\n            target.setPointerCapture(event.pointerId);\n            event.preventDefault();\n            if (context.thumbs.has(target)) {\n                target.focus();\n            } else {\n                onSlideStart(event);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerMove, (event)=>{\n            const target = event.target;\n            if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            const target = event.target;\n            if (target.hasPointerCapture(event.pointerId)) {\n                target.releasePointerCapture(event.pointerId);\n                onSlideEnd(event);\n            }\n        })\n    });\n});\nvar TRACK_NAME = \"SliderTrack\";\nvar SliderTrack = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        \"data-orientation\": context.orientation,\n        ...trackProps,\n        ref: forwardedRef\n    });\n});\nSliderTrack.displayName = TRACK_NAME;\nvar RANGE_NAME = \"SliderRange\";\nvar SliderRange = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map((value)=>convertValueToPercentage(value, context.min, context.max));\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n        \"data-orientation\": context.orientation,\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...rangeProps,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            [orientation.startEdge]: offsetStart + \"%\",\n            [orientation.endEdge]: offsetEnd + \"%\"\n        }\n    });\n});\nSliderRange.displayName = RANGE_NAME;\nvar THUMB_NAME = \"SliderThumb\";\nvar SliderThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderThumb.useComposedRefs[composedRefs]\": (node)=>setThumb(node)\n    }[\"SliderThumb.useComposedRefs[composedRefs]\"]);\n    const index = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"SliderThumb.useMemo[index]\": ()=>thumb ? getItems().findIndex({\n                \"SliderThumb.useMemo[index]\": (item)=>item.ref.current === thumb\n            }[\"SliderThumb.useMemo[index]\"]) : -1\n    }[\"SliderThumb.useMemo[index]\"], [\n        getItems,\n        thumb\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SliderThumbImpl, {\n        ...props,\n        ref: composedRefs,\n        index\n    });\n});\nvar SliderThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.useComposedRefs)(forwardedRef, {\n        \"SliderThumbImpl.useComposedRefs[composedRefs]\": (node)=>setThumb(node)\n    }[\"SliderThumbImpl.useComposedRefs[composedRefs]\"]);\n    const isFormControl = thumb ? context.form || !!thumb.closest(\"form\") : true;\n    const size = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_10__.useSize)(thumb);\n    const value = context.values[index];\n    const percent = value === void 0 ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction) : 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SliderThumbImpl.useEffect\": ()=>{\n            if (thumb) {\n                context.thumbs.add(thumb);\n                return ({\n                    \"SliderThumbImpl.useEffect\": ()=>{\n                        context.thumbs.delete(thumb);\n                    }\n                })[\"SliderThumbImpl.useEffect\"];\n            }\n        }\n    }[\"SliderThumbImpl.useEffect\"], [\n        thumb,\n        context.thumbs\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"span\", {\n        style: {\n            transform: \"var(--radix-slider-thumb-transform)\",\n            position: \"absolute\",\n            [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`\n        },\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n                scope: props.__scopeSlider,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.span, {\n                    role: \"slider\",\n                    \"aria-label\": props[\"aria-label\"] || label,\n                    \"aria-valuemin\": context.min,\n                    \"aria-valuenow\": value,\n                    \"aria-valuemax\": context.max,\n                    \"aria-orientation\": context.orientation,\n                    \"data-orientation\": context.orientation,\n                    \"data-disabled\": context.disabled ? \"\" : void 0,\n                    tabIndex: context.disabled ? void 0 : 0,\n                    ...thumbProps,\n                    ref: composedRefs,\n                    style: value === void 0 ? {\n                        display: \"none\"\n                    } : props.style,\n                    onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onFocus, ()=>{\n                        context.valueIndexToChangeRef.current = index;\n                    })\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BubbleInput, {\n                name: name ?? (context.name ? context.name + (context.values.length > 1 ? \"[]\" : \"\") : void 0),\n                form: context.form,\n                value\n            }, index)\n        ]\n    });\n});\nSliderThumb.displayName = THUMB_NAME;\nvar BubbleInput = (props)=>{\n    const { value, ...inputProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_11__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"value\");\n            const setValue = descriptor.set;\n            if (prevValue !== value && setValue) {\n                const event = new Event(\"input\", {\n                    bubbles: true\n                });\n                setValue.call(input, value);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleInput.useEffect\"], [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        style: {\n            display: \"none\"\n        },\n        ...inputProps,\n        ref,\n        defaultValue: value\n    });\n};\nfunction getNextSortedValues(prevValues = [], nextValue, atIndex) {\n    const nextValues = [\n        ...prevValues\n    ];\n    nextValues[atIndex] = nextValue;\n    return nextValues.sort((a, b)=>a - b);\n}\nfunction convertValueToPercentage(value, min, max) {\n    const maxSteps = max - min;\n    const percentPerStep = 100 / maxSteps;\n    const percentage = percentPerStep * (value - min);\n    return (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_5__.clamp)(percentage, [\n        0,\n        100\n    ]);\n}\nfunction getLabel(index, totalValues) {\n    if (totalValues > 2) {\n        return `Value ${index + 1} of ${totalValues}`;\n    } else if (totalValues === 2) {\n        return [\n            \"Minimum\",\n            \"Maximum\"\n        ][index];\n    } else {\n        return void 0;\n    }\n}\nfunction getClosestValueIndex(values, nextValue) {\n    if (values.length === 1) return 0;\n    const distances = values.map((value)=>Math.abs(value - nextValue));\n    const closestDistance = Math.min(...distances);\n    return distances.indexOf(closestDistance);\n}\nfunction getThumbInBoundsOffset(width, left, direction) {\n    const halfWidth = width / 2;\n    const halfPercent = 50;\n    const offset = linearScale([\n        0,\n        halfPercent\n    ], [\n        0,\n        halfWidth\n    ]);\n    return (halfWidth - offset(left) * direction) * direction;\n}\nfunction getStepsBetweenValues(values) {\n    return values.slice(0, -1).map((value, index)=>values[index + 1] - value);\n}\nfunction hasMinStepsBetweenValues(values, minStepsBetweenValues) {\n    if (minStepsBetweenValues > 0) {\n        const stepsBetweenValues = getStepsBetweenValues(values);\n        const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n        return actualMinStepsBetweenValues >= minStepsBetweenValues;\n    }\n    return true;\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction getDecimalCount(value) {\n    return (String(value).split(\".\")[1] || \"\").length;\n}\nfunction roundValue(value, decimalCount) {\n    const rounder = Math.pow(10, decimalCount);\n    return Math.round(value * rounder) / rounder;\n}\nvar Root = Slider;\nvar Track = SliderTrack;\nvar Range = SliderRange;\nvar Thumb = SliderThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-slider@1.2._57f520f88d0738ee932bb8049d646b3d/node_modules/@radix-ui/react-slider/dist/index.mjs\n");

/***/ })

};
;