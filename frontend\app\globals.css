@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Animation for theme switching */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-secondary;
}

::-webkit-scrollbar-thumb {
  @apply bg-border rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground;
}

/* Leaflet marker pane removal styles */
@layer utilities {
  /* 方案2：CSS 隐藏标记面板（备选方案） */
  /* 如果您不需要使用任何标记，可以取消注释下面的样式来隐藏标记面板 */
  /*
  .leaflet-marker-pane {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
  
  .leaflet-shadow-pane {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
  */
}

/* 确保地图容器样式正确 */
.leaflet-container {
  font: 12px/1.5 "Helvetica Neue", Arial, Helvetica, sans-serif;
}

/* 自定义弹出窗口样式 */
.leaflet-popup-content-wrapper {
  border-radius: 8px;
}

.leaflet-popup-content {
  margin: 12px 16px;
  line-height: 1.4;
}

/* 确保热力图矩形正确显示 */
.leaflet-overlay-pane svg {
  pointer-events: auto;
}

/* 缩放控件样式优化 */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

.leaflet-control-zoom a {
  background-color: white !important;
  color: #374151 !important;
  border: 1px solid #e5e7eb !important;
  width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  text-align: center !important;
  text-decoration: none !important;
  font-size: 18px !important;
  font-weight: bold !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f9fafb !important;
  color: #111827 !important;
}

.leaflet-control-zoom-in {
  border-radius: 4px 4px 0 0 !important;
}

.leaflet-control-zoom-out {
  border-radius: 0 0 4px 4px !important;
  border-top: none !important;
}

/* 自定义样式 */
.marine-primary {
  color: #1e3a8a;
}

.marine-bg-primary {
  background-color: #1e3a8a;
}

.leaflet-container {
  height: 100%;
  width: 100%;
}

.statistics-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-left: 4px solid #1e3a8a;
}

.species-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.species-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.map-control-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Custom popup styles for heatmap */
.custom-popup .leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.custom-popup .leaflet-popup-content {
  margin: 0;
  line-height: 1.4;
}

.custom-popup .leaflet-popup-tip {
  background: white;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Optimize rendering performance */
.leaflet-zoom-animated .leaflet-zoom-hide {
  visibility: hidden;
}

/* Smooth transitions for heatmap tiles */
.leaflet-interactive {
  transition: fill-opacity 0.2s ease;
}

/* 隐藏村庄标记的CSS样式 */
.hide-village-markers .leaflet-tile-pane {
  /* 使用CSS滤镜来处理村庄标记 */
  filter: contrast(1.05) brightness(1.02);
}

/* 针对特定地图服务的村庄标记隐藏 */
.hide-village-markers .leaflet-tile {
  /* 通过图像处理来淡化小的白色方块 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* 更精确的村庄标记过滤 - 针对OpenStreetMap */
.hide-village-markers[data-map-service="osm"] .leaflet-tile-pane::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  pointer-events: none;
  /* 使用混合模式来处理白色方块 */
  mix-blend-mode: multiply;
  opacity: 0.98;
}

/* 针对高德地图的村庄标记处理 */
.hide-village-markers[data-map-service="amap"] .leaflet-tile {
  /* 高德地图的村庄标记处理 */
  filter: hue-rotate(0deg) saturate(1.02) contrast(1.01);
}

/* 通用的村庄标记淡化处理 */
.hide-village-markers .leaflet-tile-container {
  /* 轻微的模糊处理来减少小标记的视觉冲击 */
  filter: blur(0.1px) contrast(1.02);
}

/* 确保其他地图元素不受影响 */
.hide-village-markers .leaflet-marker-pane,
.hide-village-markers .leaflet-popup-pane,
.hide-village-markers .leaflet-tooltip-pane,
.hide-village-markers .leaflet-overlay-pane {
  filter: none !important;
}

/* 为不同缩放级别提供不同的处理 */
.hide-village-markers .leaflet-zoom-animated {
  transition: filter 0.3s ease;
}

/* 在高缩放级别时减少过滤效果 */
.hide-village-markers[data-zoom-level="high"] .leaflet-tile-pane {
  filter: contrast(1.01) brightness(1.01);
}

/* 在低缩放级别时增强过滤效果 */
.hide-village-markers[data-zoom-level="low"] .leaflet-tile-pane {
  filter: contrast(1.08) brightness(1.03) blur(0.2px);
}
