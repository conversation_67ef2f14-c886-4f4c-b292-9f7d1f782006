"use client"

import { useState, useEffect } from "react"
import { MarkerLayerManager, MarkerLayerConfig } from "./marker-layer-manager"
import { Switch } from "../ui/switch"
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card"
import { Badge } from "../ui/badge"
import { Button } from "../ui/button"
import { Eye, EyeOff, Layers, MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu"

interface LayerControlPanelProps {
  markerManager: MarkerLayerManager | null
  className?: string
  compact?: boolean
}

export default function LayerControlPanel({ 
  markerManager, 
  className = "",
  compact = false 
}: LayerControlPanelProps) {
  const [layers, setLayers] = useState<MarkerLayerConfig[]>([])
  const [allVisible, setAllVisible] = useState(false)

  // 更新图层状态
  const updateLayers = () => {
    if (markerManager) {
      const currentLayers = markerManager.getAllLayers()
      setLayers(currentLayers)
      
      // 检查是否所有层都可见
      const visibleCount = currentLayers.filter(layer => layer.visible).length
      setAllVisible(visibleCount === currentLayers.length && currentLayers.length > 0)
    }
  }

  useEffect(() => {
    updateLayers()
  }, [markerManager])

  // 切换单个图层
  const toggleLayer = (layerId: string) => {
    if (markerManager) {
      markerManager.toggleLayer(layerId)
      updateLayers()
    }
  }

  // 切换所有图层
  const toggleAllLayers = () => {
    if (markerManager) {
      if (allVisible) {
        markerManager.hideAllLayers()
      } else {
        markerManager.showAllLayers()
      }
      updateLayers()
    }
  }

  // 获取图层状态统计
  const getLayerStats = () => {
    const totalLayers = layers.length
    const visibleLayers = layers.filter(layer => layer.visible).length
    const totalMarkers = layers.reduce((sum, layer) => sum + layer.markers.length, 0)
    const visibleMarkers = layers.filter(layer => layer.visible)
      .reduce((sum, layer) => sum + layer.markers.length, 0)
    
    return { totalLayers, visibleLayers, totalMarkers, visibleMarkers }
  }

  const stats = getLayerStats()

  if (compact) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-3 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Layers className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium">图层控制</span>
            <Badge variant="outline" className="text-xs">
              {stats.visibleLayers}/{stats.totalLayers}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleAllLayers}
              className="h-6 px-2 text-xs"
            >
              {allVisible ? (
                <><EyeOff className="w-3 h-3 mr-1" />隐藏</>
              ) : (
                <><Eye className="w-3 h-3 mr-1" />显示</>
              )}
            </Button>
          </div>
        </div>
        
        <div className="space-y-2">
          {layers.map((layer) => (
            <div key={layer.id} className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <div 
                  className="w-3 h-3 rounded-full border-2 border-white shadow-sm"
                  style={{ backgroundColor: layer.color || "#3388ff" }}
                />
                <span className="text-xs text-gray-700 truncate">
                  {layer.name}
                </span>
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  {layer.markers.length}
                </Badge>
              </div>
              <Switch
                checked={layer.visible}
                onCheckedChange={() => toggleLayer(layer.id)}
                className="scale-75"
              />
            </div>
          ))}
        </div>
        
        {layers.length === 0 && (
          <p className="text-xs text-gray-500 text-center py-2">
            暂无图层数据
          </p>
        )}
      </div>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            <Layers className="w-5 h-5 text-blue-600" />
            标记点图层控制
          </CardTitle>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={toggleAllLayers}>
                {allVisible ? "隐藏所有图层" : "显示所有图层"}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={updateLayers}>
                刷新图层状态
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        {/* 统计信息 */}
        <div className="flex gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <span>图层:</span>
            <Badge variant="outline" className="text-xs">
              {stats.visibleLayers}/{stats.totalLayers}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <span>标记点:</span>
            <Badge variant="outline" className="text-xs">
              {stats.visibleMarkers}/{stats.totalMarkers}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* 全局控制 */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg mb-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">所有标记点</span>
            <Badge variant="secondary" className="text-xs">
              {stats.totalMarkers}个
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500">
              {allVisible ? "全部显示" : "部分显示"}
            </span>
            <Switch
              checked={allVisible}
              onCheckedChange={toggleAllLayers}
            />
          </div>
        </div>

        {/* 图层列表 */}
        <div className="space-y-3">
          {layers.map((layer) => (
            <div key={layer.id} className="border rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
                    style={{ backgroundColor: layer.color || "#3388ff" }}
                  />
                  <span className="font-medium text-sm">{layer.name}</span>
                  <Badge variant="outline" className="text-xs">
                    {layer.markers.length}个标记
                  </Badge>
                </div>
                <Switch
                  checked={layer.visible}
                  onCheckedChange={() => toggleLayer(layer.id)}
                />
              </div>
              
              {/* 图层详细信息 */}
              <div className="text-xs text-gray-500 space-y-1">
                <div className="flex justify-between">
                  <span>状态:</span>
                  <span className={layer.visible ? "text-green-600" : "text-gray-400"}>
                    {layer.visible ? "显示中" : "已隐藏"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>标记数量:</span>
                  <span>{layer.markers.length}</span>
                </div>
                {layer.markers.length > 0 && (
                  <div className="flex justify-between">
                    <span>类型:</span>
                    <span>
                      {Array.from(new Set(layer.markers.map(m => m.category).filter(Boolean))).join(", ") || "默认"}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {layers.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Layers className="w-12 h-12 mx-auto mb-3 opacity-30" />
            <p className="text-sm">暂无图层数据</p>
            <p className="text-xs">请先添加标记点图层</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 