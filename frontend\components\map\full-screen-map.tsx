"use client"

import { useEffect, useRef, useState } from "react"
import L from "leaflet"
import "leaflet/dist/leaflet.css"
import { useMarkerLayerManager, markerIcons, MarkerLayerConfig } from "./marker-layer-manager"
import LayerControlPanel from "./layer-control-panel"

interface FullScreenMapProps {
  mapService: string
  showHeatmap: boolean
  showDistribution: boolean
  showCollectionPoints: boolean
  heatmapOpacity: number
  selectedSpecies: string
  selectedTimeRange: string
  hideVillageMarkers?: boolean // 新增属性：是否隐藏村庄标记
  showMarkerLayers?: boolean // 新增属性：是否显示marker层控制
}

export default function FullScreenMap({
  mapService,
  showHeatmap,
  showDistribution,
  showCollectionPoints,
  heatmapOpacity,
  selectedSpecies,
  selectedTimeRange,
  hideVillageMarkers = true, // 默认隐藏村庄标记
  showMarkerLayers = true, // 默认显示marker层控制
}: FullScreenMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<L.Map | null>(null)
  const layersRef = useRef<{
    heatmap?: L.LayerGroup
    distribution?: L.LayerGroup
    collectionPoints?: L.LayerGroup
  }>({})
  const markerManager = useMarkerLayerManager(mapInstanceRef.current)
  const [showLayerControl, setShowLayerControl] = useState(false)

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return

    // 初始化地图 - 聚焦中国区域
    const map = L.map(mapRef.current, {
      center: [35.0, 105.0], // 中国中心坐标
      zoom: 4, // 适合显示整个中国的缩放级别
      zoomControl: false,
      attributionControl: false,
      minZoom: 3,
      maxZoom: 18,
    })

    // 添加缩放控制器到右下角
    L.control
      .zoom({
        position: "bottomright",
      })
      .addTo(map)

    // 监听缩放事件，更新数据属性
    map.on('zoomend', () => {
      if (mapRef.current && hideVillageMarkers) {
        const currentZoom = map.getZoom()
        const zoomLevel = currentZoom >= 10 ? 'high' : currentZoom >= 6 ? 'medium' : 'low'
        mapRef.current.setAttribute('data-zoom-level', zoomLevel)
      }
    })

    mapInstanceRef.current = map

    // 初始化marker层数据
    if (markerManager && showMarkerLayers) {
      initializeMarkerLayers()
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [hideVillageMarkers])

  // 初始化marker层数据
  const initializeMarkerLayers = () => {
    if (!markerManager) return

    // 海洋生物采集点层
    const collectionLayer: MarkerLayerConfig = {
      id: "marine-collection-points",
      name: "海洋生物采集点",
      visible: showCollectionPoints,
      color: "#3388ff",
      icon: markerIcons.collection,
      markers: [
        {
          id: "collection-1",
          lat: 39.0,
          lng: 117.8,
          title: "渤海湾采集点",
          description: "江豚声学监测站",
          category: "采集点",
          popupContent: `
            <div class="p-3 min-w-48">
              <h4 class="font-semibold mb-2 text-blue-800">渤海湾采集点</h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">目标物种:</span>
                  <span class="font-medium">江豚</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">声学记录:</span>
                  <span class="font-medium">45条</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">最近更新:</span>
                  <span class="font-medium">2024-01-15</span>
                </div>
              </div>
            </div>
          `
        },
        {
          id: "collection-2",
          lat: 31.2,
          lng: 121.8,
          title: "长江口采集点",
          description: "中华白海豚监测站",
          category: "采集点"
        },
        {
          id: "collection-3",
          lat: 24.5,
          lng: 118.2,
          title: "台湾海峡采集点",
          description: "台湾白海豚监测站",
          category: "采集点"
        },
        {
          id: "collection-4",
          lat: 22.3,
          lng: 114.1,
          title: "珠江口采集点",
          description: "中华白海豚保护区",
          category: "采集点"
        },
        {
          id: "collection-5",
          lat: 20.2,
          lng: 110.8,
          title: "海南北部采集点",
          description: "热带海豚监测站",
          category: "采集点"
        },
        {
          id: "collection-6",
          lat: 16.8,
          lng: 112.3,
          title: "西沙群岛采集点",
          description: "深海鲸类监测站",
          category: "采集点"
        }
      ]
    }

    // 科研观测点层
    const researchLayer: MarkerLayerConfig = {
      id: "research-stations",
      name: "海洋科研观测站",
      visible: false,
      color: "#f59e0b",
      icon: markerIcons.research,
      markers: [
        {
          id: "research-1",
          lat: 38.5,
          lng: 119.0,
          title: "渤海海洋科学研究站",
          description: "海洋生态系统研究",
          category: "科研站"
        },
        {
          id: "research-2",
          lat: 30.0,
          lng: 122.0,
          title: "东海海洋研究所",
          description: "海洋生物多样性研究",
          category: "科研站"
        },
        {
          id: "research-3",
          lat: 21.0,
          lng: 112.0,
          title: "南海海洋科学院",
          description: "深海生物研究中心",
          category: "科研站"
        }
      ]
    }

    // 环境监测点层
    const monitoringLayer: MarkerLayerConfig = {
      id: "monitoring-stations",
      name: "海洋环境监测站",
      visible: false,
      color: "#ef4444",
      icon: markerIcons.monitoring,
      markers: [
        {
          id: "monitoring-1",
          lat: 40.0,
          lng: 120.5,
          title: "辽东湾监测站",
          description: "水质与声环境监测",
          category: "监测站"
        },
        {
          id: "monitoring-2",
          lat: 36.0,
          lng: 120.0,
          title: "黄海南部监测站",
          description: "海洋噪音监测",
          category: "监测站"
        },
        {
          id: "monitoring-3",
          lat: 28.0,
          lng: 121.0,
          title: "东海南部监测站",
          description: "船舶噪音影响监测",
          category: "监测站"
        },
        {
          id: "monitoring-4",
          lat: 18.5,
          lng: 109.5,
          title: "海南西部监测站",
          description: "渔业活动监测",
          category: "监测站"
        }
      ]
    }

    // 添加所有marker层
    markerManager.addMarkerLayer(collectionLayer)
    markerManager.addMarkerLayer(researchLayer)
    markerManager.addMarkerLayer(monitoringLayer)

    setShowLayerControl(true)
  }

  // 更新底图 - 使用中国地图服务
  useEffect(() => {
    if (!mapInstanceRef.current) return

    const map = mapInstanceRef.current

    // 移除现有底图
    map.eachLayer((layer) => {
      if (layer instanceof L.TileLayer) {
        map.removeLayer(layer)
      }
    })

    // 添加中国地图服务
    let tileUrl = ""
    let attribution = ""

    switch (mapService) {
      case "osm":
        // 使用OpenStreetMap但聚焦中国区域
        tileUrl = "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution = "© OpenStreetMap contributors"
        break
      case "esri-world":
        tileUrl = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}"
        attribution = "© Esri"
        break
      case "esri-satellite":
        tileUrl = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
        attribution = "© Esri"
        break
      case "cartodb-light":
        tileUrl = "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
        attribution = "© CartoDB"
        break
      case "cartodb-dark":
        tileUrl = "https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png"
        attribution = "© CartoDB"
        break
      case "osm-no-labels":
        // 使用无标签的OpenStreetMap样式
        tileUrl = "https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png"
        attribution = "© CartoDB, © OpenStreetMap contributors"
        break
      case "tianditu":
        // 天地图服务 - 中国官方地图
        tileUrl =
          "https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=YOUR_API_KEY"
        attribution = "© 天地图"
        break
      case "amap":
        // 高德地图
        tileUrl = "https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}"
        attribution = "© 高德地图"
        break
      default:
        tileUrl = "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        attribution = "© OpenStreetMap contributors"
    }

    // 对于高德地图，需要特殊处理subdomains
    const tileLayerOptions: L.TileLayerOptions = {
      attribution,
      maxZoom: 18,
      subdomains: mapService === "amap" ? ["1", "2", "3", "4"] : ["a", "b", "c"],
      // 添加CSS类名以便后续样式处理
      className: hideVillageMarkers ? 'hide-village-markers' : '',
    }

    L.tileLayer(tileUrl, tileLayerOptions).addTo(map)
  }, [mapService, hideVillageMarkers])

  // 更新热力图层 - 聚焦中国海域
  useEffect(() => {
    if (!mapInstanceRef.current) return

    const map = mapInstanceRef.current

    // 移除现有热力图层
    if (layersRef.current.heatmap) {
      map.removeLayer(layersRef.current.heatmap)
    }

    if (showHeatmap) {
      const heatmapLayer = L.layerGroup()

      // 中国海域热力图实现
      const createChinaMarineHeatmap = () => {
        const currentZoom = map.getZoom()
        const bounds = map.getBounds()

        // 根据缩放级别动态调整网格大小
        let gridSize = 1.0 // 基础网格大小
        if (currentZoom >= 8) gridSize = 0.2
        else if (currentZoom >= 6) gridSize = 0.4
        else if (currentZoom >= 4) gridSize = 0.8
        else gridSize = 1.2

        // 中国海域热点数据 - 包含台湾海域
        const chinaMarineHotspots = [
          // 渤海湾区域
          { lat: 39.0, lng: 117.5, intensity: 0.8, radius: 4, name: "渤海湾" },
          { lat: 38.5, lng: 119.0, intensity: 0.7, radius: 3, name: "渤海中部" },
          { lat: 40.0, lng: 120.5, intensity: 0.6, radius: 3, name: "辽东湾" },

          // 黄海区域
          { lat: 36.0, lng: 120.0, intensity: 0.9, radius: 5, name: "黄海南部" },
          { lat: 35.0, lng: 119.5, intensity: 0.8, radius: 4, name: "黄海中部" },
          { lat: 37.5, lng: 122.0, intensity: 0.7, radius: 3, name: "黄海北部" },

          // 东海区域
          { lat: 31.0, lng: 122.0, intensity: 0.9, radius: 5, name: "长江口" },
          { lat: 29.5, lng: 122.5, intensity: 0.8, radius: 4, name: "东海中部" },
          { lat: 28.0, lng: 121.0, intensity: 0.7, radius: 4, name: "东海南部" },
          { lat: 30.0, lng: 123.5, intensity: 0.6, radius: 3, name: "东海外海" },

          // 台湾海峡及周边海域
          { lat: 24.5, lng: 118.5, intensity: 0.8, radius: 4, name: "台湾海峡北部" },
          { lat: 23.5, lng: 118.0, intensity: 0.7, radius: 3, name: "台湾海峡中部" },
          { lat: 22.5, lng: 117.5, intensity: 0.6, radius: 3, name: "台湾海峡南部" },

          // 台湾东部海域
          { lat: 24.0, lng: 121.5, intensity: 0.8, radius: 4, name: "台湾东海岸" },
          { lat: 23.0, lng: 121.3, intensity: 0.7, radius: 3, name: "花莲外海" },
          { lat: 22.0, lng: 121.0, intensity: 0.6, radius: 3, name: "台东外海" },

          // 南海北部区域
          { lat: 22.0, lng: 114.0, intensity: 0.9, radius: 5, name: "珠江口" },
          { lat: 21.0, lng: 112.0, intensity: 0.8, radius: 4, name: "南海北部" },
          { lat: 20.0, lng: 111.0, intensity: 0.7, radius: 4, name: "海南北部" },
          { lat: 18.5, lng: 109.5, intensity: 0.6, radius: 3, name: "海南西部" },

          // 南海中部区域
          { lat: 16.0, lng: 112.0, intensity: 0.7, radius: 4, name: "西沙群岛" },
          { lat: 15.0, lng: 113.5, intensity: 0.6, radius: 3, name: "南海中部" },
          { lat: 12.0, lng: 114.0, intensity: 0.5, radius: 3, name: "南沙群岛北部" },

          // 南海南部区域
          { lat: 9.0, lng: 114.5, intensity: 0.6, radius: 3, name: "南沙群岛" },
          { lat: 8.0, lng: 112.0, intensity: 0.5, radius: 2, name: "南海南部" },

          // 钓鱼岛及周边海域
          { lat: 25.7, lng: 123.5, intensity: 0.7, radius: 2, name: "钓鱼岛海域" },
        ]

        // 创建网格映射以避免重复瓦片
        const gridMap = new Map<string, { intensity: number; count: number; name: string }>()

        // 处理每个热点并生成周围的网格瓦片
        chinaMarineHotspots.forEach((hotspot) => {
          const maxRadius = Math.min(hotspot.radius, 6)

          for (let latOffset = -maxRadius; latOffset <= maxRadius; latOffset++) {
            for (let lngOffset = -maxRadius; lngOffset <= maxRadius; lngOffset++) {
              const distance = Math.sqrt(latOffset * latOffset + lngOffset * lngOffset)

              if (distance <= maxRadius) {
                const cellLat = hotspot.lat + latOffset * gridSize
                const cellLng = hotspot.lng + lngOffset * gridSize

                // 对齐到网格
                const gridLat = Math.round(cellLat / gridSize) * gridSize
                const gridLng = Math.round(cellLng / gridSize) * gridSize

                const gridKey = `${gridLat.toFixed(3)}_${gridLng.toFixed(3)}`

                // 基于距离计算强度
                const distanceRatio = 1 - distance / maxRadius
                // 使用位置坐标作为种子生成固定的伪随机值，避免Math.random()
                const seed = (gridLat * 1000 + gridLng * 1000) % 1000
                const pseudoRandom = (seed * 9301 + 49297) % 233280 / 233280
                const cellIntensity = hotspot.intensity * distanceRatio * (0.5 + pseudoRandom * 0.5)

                // 只保留有意义的强度值
                if (cellIntensity >= 0.3) {
                  if (gridMap.has(gridKey)) {
                    const existing = gridMap.get(gridKey)!
                    existing.intensity = Math.min(1.0, existing.intensity + cellIntensity * 0.4)
                    existing.count++
                  } else {
                    gridMap.set(gridKey, {
                      intensity: cellIntensity,
                      count: 1,
                      name: hotspot.name,
                    })
                  }
                }
              }
            }
          }
        })

        // 渲染网格瓦片
        const tileBatch: L.Rectangle[] = []

        gridMap.forEach((data, gridKey) => {
          const [latStr, lngStr] = gridKey.split("_")
          const gridLat = Number.parseFloat(latStr)
          const gridLng = Number.parseFloat(lngStr)

          // 跳过低强度瓦片 - 只显示有颜色的
          if (data.intensity < 0.35) return

          // 检查是否在当前视图范围内
          const buffer = gridSize * 2
          if (
            gridLat < bounds.getSouth() - buffer ||
            gridLat > bounds.getNorth() + buffer ||
            gridLng < bounds.getWest() - buffer ||
            gridLng > bounds.getEast() + buffer
          ) {
            return
          }

          // 定义方形边界
          const tileBounds: L.LatLngBoundsExpression = [
            [gridLat - gridSize / 2, gridLng - gridSize / 2],
            [gridLat + gridSize / 2, gridLng + gridSize / 2],
          ]

          // 颜色映射 - 去除白色和浅色
          let color = "#10b981" // 默认翠绿色
          let speciesCount = Math.floor(data.intensity * 500)

          if (data.intensity >= 0.9) {
            color = "#dc2626" // 红色 450+
            speciesCount = Math.floor(450 + data.intensity * 150)
          } else if (data.intensity >= 0.8) {
            color = "#ea580c" // 橙红色 400-450
          } else if (data.intensity >= 0.7) {
            color = "#f97316" // 橙色 350-400
          } else if (data.intensity >= 0.6) {
            color = "#f59e0b" // 琥珀色 300-350
          } else if (data.intensity >= 0.5) {
            color = "#eab308" // 黄色 250-300
          } else if (data.intensity >= 0.45) {
            color = "#84cc16" // 青柠色 200-250
          } else if (data.intensity >= 0.4) {
            color = "#22c55e" // 绿色 150-200
          } else {
            color = "#10b981" // 翠绿色 100-150
          }

          // 创建矩形瓦片
          const rectangle = L.rectangle(tileBounds, {
            color: color,
            fillColor: color,
            fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100), 0.8),
            weight: 0,
            stroke: false,
            interactive: true,
          })

          // 悬停效果
          rectangle.on("mouseover", function () {
            this.setStyle({
              fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100) + 0.15, 0.9),
            })
          })

          rectangle.on("mouseout", function () {
            this.setStyle({
              fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100), 0.8),
            })
          })

          // 详细弹窗
          rectangle.bindPopup(
            `
            <div class="p-3 min-w-52">
              <h4 class="font-semibold mb-2 text-blue-800">${data.name}海域</h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">海洋物种:</span>
                  <span class="font-bold text-lg" style="color: ${color}">${speciesCount}种</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">声学记录:</span>
                  <span class="font-medium">${Math.floor(speciesCount * 2.8)}条</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">生物密度:</span>
                  <span class="font-medium">${
                    data.intensity >= 0.8
                      ? "极高"
                      : data.intensity >= 0.6
                        ? "高"
                        : data.intensity >= 0.45
                          ? "中"
                          : "中低"
                  }</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">海域坐标:</span>
                  <span class="font-mono text-xs">${gridLat.toFixed(2)}°N, ${gridLng.toFixed(2)}°E</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">监测站点:</span>
                  <span class="text-xs">${data.count}个</span>
                </div>
              </div>
              <div class="mt-3 flex space-x-2">
                <button class="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors">
                  详细数据
                </button>
                <button class="px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600 transition-colors">
                  播放录音
                </button>
              </div>
            </div>
          `,
            {
              maxWidth: 320,
              className: "custom-popup",
            },
          )

          tileBatch.push(rectangle)
        })

        // 批量添加瓦片
        tileBatch.forEach((tile) => tile.addTo(heatmapLayer))

        console.log(`渲染了 ${tileBatch.length} 个中国海域热力图瓦片，缩放级别 ${currentZoom}`)
      }

      createChinaMarineHeatmap()

      // 地图移动或缩放时更新热力图
      const updateHeatmapHandler = L.Util.throttle(() => {
        if (showHeatmap && layersRef.current.heatmap) {
          map.removeLayer(layersRef.current.heatmap)
          const newHeatmapLayer = L.layerGroup()
          layersRef.current.heatmap = newHeatmapLayer
          createChinaMarineHeatmap()
          newHeatmapLayer.addTo(map)
        }
      }, 400)

      map.on("zoomend moveend", updateHeatmapHandler)

      layersRef.current.heatmap = heatmapLayer
      heatmapLayer.addTo(map)

      return () => {
        map.off("zoomend moveend", updateHeatmapHandler)
      }
    }
  }, [showHeatmap, heatmapOpacity, selectedSpecies, selectedTimeRange])

  // 更新分布图层
  useEffect(() => {
    if (!mapInstanceRef.current) return

    const map = mapInstanceRef.current

    if (layersRef.current.distribution) {
      map.removeLayer(layersRef.current.distribution)
    }

    if (showDistribution) {
      const distributionLayer = L.layerGroup()

      // 中国海域物种分布区域
      const chinaDistributionAreas = [
        { lat: 38.0, lng: 119.0, radius: 300000, name: "渤海黄海鲸豚分布区" },
        { lat: 30.0, lng: 122.0, radius: 400000, name: "东海江豚分布区" },
        { lat: 24.0, lng: 118.5, radius: 350000, name: "台湾海峡中华白海豚分布区" },
        { lat: 20.0, lng: 112.0, radius: 500000, name: "南海鲸类分布区" },
      ]

      chinaDistributionAreas.forEach((area, index) => {
        const colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444"]
        L.circle([area.lat, area.lng], {
          color: colors[index % colors.length],
          fillColor: colors[index % colors.length],
          fillOpacity: 0.15,
          radius: area.radius,
          weight: 2,
        })
          .addTo(distributionLayer)
          .bindPopup(`
          <div class="p-2">
            <h4 class="font-semibold">${area.name}</h4>
            <p class="text-sm text-gray-600">重要海洋生物栖息和活动区域</p>
          </div>
        `)
      })

      layersRef.current.distribution = distributionLayer
      distributionLayer.addTo(map)
    }
  }, [showDistribution])

  // 更新采集点图层
  useEffect(() => {
    if (!mapInstanceRef.current) return

    const map = mapInstanceRef.current

    if (layersRef.current.collectionPoints) {
      map.removeLayer(layersRef.current.collectionPoints)
    }

    if (showCollectionPoints) {
      const collectionPointsLayer = L.layerGroup()

      // 中国海域采集点数据
      const chinaCollectionPoints = [
        { lat: 39.0, lng: 117.8, species: "江豚", recordings: 45, date: "2024-01-15" },
        { lat: 31.2, lng: 121.8, species: "中华白海豚", recordings: 32, date: "2024-01-14" },
        { lat: 24.5, lng: 118.2, species: "台湾白海豚", recordings: 28, date: "2024-01-13" },
        { lat: 22.3, lng: 114.1, species: "中华白海豚", recordings: 38, date: "2024-01-12" },
        { lat: 20.2, lng: 110.8, species: "热带海豚", recordings: 25, date: "2024-01-11" },
        { lat: 16.8, lng: 112.3, species: "西沙鲸类", recordings: 19, date: "2024-01-10" },
      ]

      chinaCollectionPoints.forEach((point) => {
        L.marker([point.lat, point.lng])
          .addTo(collectionPointsLayer)
          .bindPopup(`
            <div class="p-3 min-w-48">
              <h4 class="font-semibold mb-2">${point.species}监测点</h4>
              <div class="space-y-1 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">声学记录:</span>
                  <span class="font-medium">${point.recordings}条</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">最近更新:</span>
                  <span class="font-medium">${point.date}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">坐标:</span>
                  <span class="font-mono text-xs">${point.lat.toFixed(2)}°N, ${point.lng.toFixed(2)}°E</span>
                </div>
              </div>
              <div class="mt-3 flex space-x-2">
                <button class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600">
                  播放录音
                </button>
                <button class="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600">
                  查看详情
                </button>
              </div>
            </div>
          `)
      })

      layersRef.current.collectionPoints = collectionPointsLayer
      collectionPointsLayer.addTo(map)
    }
  }, [showCollectionPoints, selectedSpecies, selectedTimeRange])

  return (
    <div className="relative w-full h-full">
      <div 
        ref={mapRef} 
        className={`w-full h-full ${hideVillageMarkers ? 'hide-village-markers' : ''}`}
        data-map-service={mapService}
        data-zoom-level="medium"
        style={{
          // 添加CSS过滤器来隐藏白色方块村庄标记
          ...(hideVillageMarkers && {
            filter: 'contrast(1.1) saturate(1.05)',
          })
        }}
      />
      
      {/* 图层控制面板 */}
      {showLayerControl && markerManager && showMarkerLayers && (
        <div className="absolute top-4 right-4 z-[1000]">
          <LayerControlPanel 
            markerManager={markerManager}
            compact={false}
            className="w-80 max-h-96 overflow-y-auto"
          />
        </div>
      )}
    </div>
  )
}
