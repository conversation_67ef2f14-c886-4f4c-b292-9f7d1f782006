# Leaflet Marker 层管理系统

这是一个基于 Leaflet 和 React 的可控制标记点层管理系统，支持动态显示/隐藏、分组管理和图层控制。

## 核心组件

### 1. MarkerLayerManager 类

主要的标记点层管理器，提供完整的图层管理功能。

#### 主要方法：

- `addMarkerLayer(config)` - 添加标记点层
- `showLayer(layerId)` - 显示指定层
- `hideLayer(layerId)` - 隐藏指定层
- `toggleLayer(layerId)` - 切换层显示状态
- `showAllLayers()` - 显示所有层
- `hideAllLayers()` - 隐藏所有层
- `removeLayer(layerId)` - 移除指定层
- `updateLayer(layerId, newConfig)` - 更新层配置

### 2. useMarkerLayerManager Hook

React Hook，用于在组件中使用 MarkerLayerManager。

```tsx
const markerManager = useMarkerLayerManager(mapInstance)
```

### 3. LayerControlPanel 组件

图层控制面板UI组件，提供可视化的图层开关控制。

## 使用方法

### 基本用法

```tsx
import { useMarkerLayerManager, markerIcons, MarkerLayerConfig } from "./marker-layer-manager"
import LayerControlPanel from "./layer-control-panel"

function MyMapComponent() {
  const mapRef = useRef<L.Map | null>(null)
  const markerManager = useMarkerLayerManager(mapRef.current)

  useEffect(() => {
    if (markerManager) {
      // 配置标记点层
      const myLayer: MarkerLayerConfig = {
        id: "my-markers",
        name: "我的标记点",
        visible: true,
        color: "#3388ff",
        icon: markerIcons.collection,
        markers: [
          {
            id: "marker-1",
            lat: 39.0,
            lng: 117.8,
            title: "标记点1",
            description: "这是第一个标记点",
            category: "采集点"
          }
        ]
      }
      
      markerManager.addMarkerLayer(myLayer)
    }
  }, [markerManager])

  return (
    <div className="relative">
      <div ref={mapRef} className="w-full h-96" />
      {markerManager && (
        <LayerControlPanel 
          markerManager={markerManager}
          compact={true}
        />
      )}
    </div>
  )
}
```

### 层配置选项

```tsx
interface MarkerLayerConfig {
  id: string           // 唯一标识符
  name: string         // 显示名称
  visible: boolean     // 初始可见状态
  markers: MarkerData[] // 标记点数据数组
  icon?: L.Icon        // 默认图标
  color?: string       // 层颜色（用于UI显示）
}

interface MarkerData {
  id: string           // 标记点ID
  lat: number          // 纬度
  lng: number          // 经度
  title: string        // 标题
  description?: string // 描述
  category?: string    // 分类
  icon?: L.Icon        // 自定义图标
  popupContent?: string // 自定义弹窗内容
}
```

### 预定义图标

系统提供了多种预定义图标：

```tsx
import { markerIcons } from "./marker-layer-manager"

// 可用图标：
markerIcons.collection  // 蓝色 - 采集点
markerIcons.observation // 绿色 - 观测点
markerIcons.research    // 橙色 - 科研站
markerIcons.monitoring  // 红色 - 监测点
markerIcons.default     // 灰色 - 默认
```

### 层控制面板选项

```tsx
<LayerControlPanel 
  markerManager={markerManager}
  compact={true}           // 紧凑模式
  className="w-64"         // 自定义样式
/>
```

## 高级功能

### 1. 动态更新层数据

```tsx
// 更新现有层的标记点
markerManager.updateLayer("my-layer", {
  markers: newMarkersData
})
```

### 2. 批量控制

```tsx
// 显示所有层
markerManager.showAllLayers()

// 隐藏所有层
markerManager.hideAllLayers()
```

### 3. 获取统计信息

```tsx
// 获取层的标记点数量
const count = markerManager.getLayerMarkerCount("my-layer")

// 获取所有可见标记点数量
const visibleCount = markerManager.getVisibleMarkerCount()

// 获取所有层配置
const allLayers = markerManager.getAllLayers()
```

### 4. 自定义弹窗内容

```tsx
const marker = {
  id: "custom-marker",
  lat: 39.0,
  lng: 117.8,
  title: "自定义标记",
  popupContent: `
    <div class="p-3">
      <h4 class="font-bold">自定义标题</h4>
      <p>这是自定义的弹窗内容</p>
      <button onclick="alert('点击了按钮!')">点击我</button>
    </div>
  `
}
```

## 集成到现有项目

### 1. 修改地图组件

在现有的地图组件中添加 marker 层管理：

```tsx
// 导入必要组件
import { useMarkerLayerManager } from "./marker-layer-manager"
import LayerControlPanel from "./layer-control-panel"

// 在组件中使用
const markerManager = useMarkerLayerManager(mapInstance)
const [showLayerControl, setShowLayerControl] = useState(false)

// 在地图初始化后添加层数据
useEffect(() => {
  if (mapInstance && markerManager) {
    // 添加你的标记点层
    initializeMarkerLayers()
  }
}, [mapInstance, markerManager])
```

### 2. 添加控制面板UI

```tsx
return (
  <div className="relative">
    <div ref={mapRef} />
    {showLayerControl && markerManager && (
      <div className="absolute top-4 right-4 z-[1000]">
        <LayerControlPanel 
          markerManager={markerManager}
          compact={window.innerWidth < 768}
        />
      </div>
    )}
  </div>
)
```

## 注意事项

1. **性能优化**：大量标记点时建议使用分层加载或聚合显示
2. **内存管理**：组件卸载时会自动清理资源
3. **样式兼容**：确保 Tailwind CSS 样式可用
4. **类型安全**：使用 TypeScript 获得更好的开发体验

## 示例项目

参考 `home-page-map.tsx` 和 `full-screen-map.tsx` 中的完整实现示例。 