"use client"

import { useState, useEffect } from "react"
import MainLayout from "@/components/layout/main-layout"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Heart, Share2, Download, Play, Pause, Volume2, MapPin, Calendar, ChevronDown, ChevronUp } from "lucide-react"
import Image from "next/image"
import dynamic from "next/dynamic"

// 动态导入地图组件
const SpeciesMap = dynamic(() => import("@/components/map/species-map"), {
  ssr: false,
  loading: () => <div className="w-full h-64 bg-gray-100 animate-pulse rounded-lg"></div>,
})

interface SpeciesDetailPageProps {
  speciesId: string
}

interface SpeciesDetail {
  id: string
  name: string
  scientificName: string
  category: string
  habitat: string
  description: string
  fullDescription: string
  images: string[]
  recordings: Recording[]
  stats: {
    totalRecordings: number
    observations: number
    lastUpdated: string
  }
  distribution: string[]
  relatedSpecies: RelatedSpecies[]
}

interface Recording {
  id: string
  title: string
  duration: string
  location: string
  date: string
  audioUrl: string
}

interface RelatedSpecies {
  id: string
  name: string
  scientificName: string
  image: string
}

export default function SpeciesDetailPage({ speciesId }: SpeciesDetailPageProps) {
  const [species, setSpecies] = useState<SpeciesDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [isFavorited, setIsFavorited] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentRecording, setCurrentRecording] = useState(0)
  const [showFullDescription, setShowFullDescription] = useState(false)

  useEffect(() => {
    // 模拟获取物种详情数据
    const mockSpeciesDetail: SpeciesDetail = {
      id: speciesId,
      name: "蓝鲸",
      scientificName: "Balaenoptera musculus",
      category: "哺乳动物",
      habitat: "深海",
      description:
        "蓝鲸是世界上最大的动物，成年蓝鲸体长可达30米，重达200吨。它们以磷虾为主要食物，具有独特的低频歌声。",
      fullDescription: `蓝鲸（学名：Balaenoptera musculus）是须鲸科须鲸属的一种海洋哺乳动物，被认为是已知的地球上生存过的体积最大的动物，长可达33米，重达200吨。

蓝鲸的身体修长，背部是青灰色的，不过在水中看起来有时颜色会比较淡。与其他须鲸一样，蓝鲸主要以小型的甲壳类（例如磷虾）与小型鱼类为食，有时也包括鱿鱼。

蓝鲸的声音交流系统非常复杂，它们能够发出频率在10-40赫兹之间的低频声音，这些声音可以在海洋中传播数百公里。科学家认为这些声音用于长距离通信、导航和寻找配偶。

目前全球蓝鲸数量估计在10,000-25,000头之间，主要分布在南极海域、北太平洋和北大西洋。由于历史上的大规模捕鲸活动，蓝鲸一度濒临灭绝，现在受到国际保护。`,
      images: [
        "/placeholder.svg?height=400&width=600",
        "/placeholder.svg?height=400&width=600",
        "/placeholder.svg?height=400&width=600",
      ],
      recordings: [
        {
          id: "1",
          title: "蓝鲸低频歌声",
          duration: "3:45",
          location: "太平洋东北部",
          date: "2024-01-15",
          audioUrl: "/placeholder-audio.mp3",
        },
        {
          id: "2",
          title: "蓝鲸觅食声音",
          duration: "2:18",
          location: "南极海域",
          date: "2024-01-10",
          audioUrl: "/placeholder-audio.mp3",
        },
        {
          id: "3",
          title: "蓝鲸群体交流",
          duration: "5:22",
          location: "加利福尼亚海岸",
          date: "2024-01-08",
          audioUrl: "/placeholder-audio.mp3",
        },
      ],
      stats: {
        totalRecordings: 156,
        observations: 89,
        lastUpdated: "2024-01-15",
      },
      distribution: ["太平洋", "大西洋", "印度洋", "南极海域"],
      relatedSpecies: [
        {
          id: "2",
          name: "座头鲸",
          scientificName: "Megaptera novaeangliae",
          image: "/placeholder.svg?height=100&width=100",
        },
        {
          id: "5",
          name: "虎鲸",
          scientificName: "Orcinus orca",
          image: "/placeholder.svg?height=100&width=100",
        },
        {
          id: "6",
          name: "灰鲸",
          scientificName: "Eschrichtius robustus",
          image: "/placeholder.svg?height=100&width=100",
        },
      ],
    }

    setTimeout(() => {
      setSpecies(mockSpeciesDetail)
      setLoading(false)
    }, 1000)
  }, [speciesId])

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-12 bg-gray-200 rounded w-2/3 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-6">
                <div className="h-64 bg-gray-200 rounded-lg"></div>
                <div className="h-48 bg-gray-200 rounded-lg"></div>
              </div>
              <div className="space-y-6">
                <div className="h-32 bg-gray-200 rounded-lg"></div>
                <div className="h-48 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (!species) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">物种未找到</h1>
            <p className="text-gray-600">抱歉，未找到指定的物种信息。</p>
            <Button className="mt-4" onClick={() => (window.location.href = "/species")}>
              返回搜索页面
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-blue-600 hover:text-blue-800">
                首页
              </a>
            </li>
            <li className="text-gray-500">/</li>
            <li>
              <a href="/species" className="text-blue-600 hover:text-blue-800">
                数据搜索
              </a>
            </li>
            <li className="text-gray-500">/</li>
            <li className="text-gray-900">{species.name}</li>
          </ol>
        </nav>

        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold marine-primary mb-2">{species.name}</h1>
            <p className="text-lg text-gray-600 italic">{species.scientificName}</p>
          </div>
          <div className="flex space-x-2 mt-4 sm:mt-0">
            <Button variant={isFavorited ? "default" : "outline"} onClick={() => setIsFavorited(!isFavorited)}>
              <Heart className={`w-4 h-4 mr-2 ${isFavorited ? "fill-current" : ""}`} />
              {isFavorited ? "已收藏" : "收藏"}
            </Button>
            <Button variant="outline">
              <Share2 className="w-4 h-4 mr-2" />
              分享
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Image
                      src={species.images[0] || "/placeholder.svg"}
                      alt={species.name}
                      width={400}
                      height={300}
                      className="w-full h-64 object-cover rounded-lg"
                    />
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">中文名</label>
                      <p className="text-lg font-semibold">{species.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">学名</label>
                      <p className="text-lg italic">{species.scientificName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">分类</label>
                      <Badge className="ml-2">{species.category}</Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">栖息环境</label>
                      <Badge variant="outline" className="ml-2">
                        {species.habitat}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">分布区域</label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {species.distribution.map((area) => (
                          <Badge key={area} variant="secondary">
                            {area}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Audio Player */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Volume2 className="w-5 h-5 mr-2" />
                  声学特征
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="player" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="player">音频播放</TabsTrigger>
                    <TabsTrigger value="list">录音列表</TabsTrigger>
                  </TabsList>

                  <TabsContent value="player" className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h4 className="font-semibold">{species.recordings[currentRecording]?.title}</h4>
                          <p className="text-sm text-gray-600">
                            {species.recordings[currentRecording]?.location} •{" "}
                            {species.recordings[currentRecording]?.date}
                          </p>
                        </div>
                        <Badge>{species.recordings[currentRecording]?.duration}</Badge>
                      </div>

                      {/* 模拟波形显示 */}
                      <div className="h-24 bg-blue-100 rounded mb-4 flex items-center justify-center">
                        <div className="flex items-end space-x-1">
                          {[...Array(50)].map((_, i) => {
                            // 使用固定的伪随机高度避免水合不匹配
                            const height = ((i * 7 + 13) % 50) + 10;
                            return (
                              <div
                                key={i}
                                className="bg-blue-500 w-1"
                                style={{ height: `${height}px` }}
                              />
                            );
                          })}
                        </div>
                      </div>

                      <div className="flex items-center justify-center space-x-4">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => setCurrentRecording(Math.max(0, currentRecording - 1))}
                          disabled={currentRecording === 0}
                        >
                          ⏮
                        </Button>
                        <Button size="icon" onClick={handlePlayPause} className="w-12 h-12">
                          {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() =>
                            setCurrentRecording(Math.min(species.recordings.length - 1, currentRecording + 1))
                          }
                          disabled={currentRecording === species.recordings.length - 1}
                        >
                          ⏭
                        </Button>
                        <Button variant="outline" size="icon">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="list">
                    <div className="space-y-2">
                      {species.recordings.map((recording, index) => (
                        <div
                          key={recording.id}
                          className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                            index === currentRecording ? "bg-blue-50 border-blue-200" : "hover:bg-gray-50"
                          }`}
                          onClick={() => setCurrentRecording(index)}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{recording.title}</h4>
                              <p className="text-sm text-gray-600">
                                {recording.location} • {recording.date}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">{recording.duration}</Badge>
                              <Button size="icon" variant="ghost" className="w-8 h-8">
                                <Play className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>物种描述</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p className="text-gray-700 leading-relaxed mb-4">{species.description}</p>

                  {showFullDescription && (
                    <div className="text-gray-700 leading-relaxed whitespace-pre-line">{species.fullDescription}</div>
                  )}

                  <Button
                    variant="ghost"
                    onClick={() => setShowFullDescription(!showFullDescription)}
                    className="mt-4 p-0 h-auto font-normal text-blue-600 hover:text-blue-800"
                  >
                    {showFullDescription ? (
                      <>
                        <ChevronUp className="w-4 h-4 mr-1" />
                        收起
                      </>
                    ) : (
                      <>
                        <ChevronDown className="w-4 h-4 mr-1" />
                        显示更多
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Sidebar */}
          <div className="space-y-6">
            {/* Distribution Map */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="w-5 h-5 mr-2" />
                  分布地图
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SpeciesMap speciesId={species.id} />
              </CardContent>
            </Card>

            {/* Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>统计信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">录音总数</span>
                  <span className="text-2xl font-bold text-green-600">{species.stats.totalRecordings}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">观测记录</span>
                  <span className="text-2xl font-bold text-blue-600">{species.stats.observations}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">最新更新</span>
                  <span className="text-sm text-gray-500 flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {species.stats.lastUpdated}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Related Species */}
            <Card>
              <CardHeader>
                <CardTitle>相关物种</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {species.relatedSpecies.map((relatedSpecies) => (
                    <div
                      key={relatedSpecies.id}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer"
                      onClick={() => (window.location.href = `/species/${relatedSpecies.id}`)}
                    >
                      <Image
                        src={relatedSpecies.image || "/placeholder.svg"}
                        alt={relatedSpecies.name}
                        width={48}
                        height={48}
                        className="w-12 h-12 object-cover rounded-lg"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate">{relatedSpecies.name}</h4>
                        <p className="text-xs text-gray-500 italic truncate">{relatedSpecies.scientificName}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
