"use client"

import { useEffect, useRef, useState } from "react"
import L from "leaflet"
import "leaflet/dist/leaflet.css"
import { useMarkerLayerManager, markerIcons, MarkerLayerConfig } from "./marker-layer-manager"
import LayerControlPanel from "./layer-control-panel"

export default function HomePageMap() {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<L.Map | null>(null)
  const markerManager = useMarkerLayerManager(mapInstanceRef.current)
  const [showLayerControl, setShowLayerControl] = useState(false)

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return

    // 初始化地图
    const map = L.map(mapRef.current, {
      center: [35.0, 105.0], // 调整中心点到中国中部
      zoom: 4, // 调整缩放级别以更好显示中国
      zoomControl: false,
      attributionControl: false,
    })

    // 移除标记面板的函数 - 方案1：JavaScript 动态移除（推荐）
    const removeMarkerPane = () => {
      const markerPane = map.getPane('markerPane')
      if (markerPane && markerPane.parentNode) {
        markerPane.parentNode.removeChild(markerPane)
        console.log('标记面板已成功移除')
      }
    }

    // 选择是否移除标记面板
    // 如果您不需要使用任何标记，可以取消注释下面的代码来移除标记面板
    // map.whenReady(() => {
    //   // 使用 setTimeout 确保所有面板都已创建完成
    //   setTimeout(removeMarkerPane, 100)
    // })

    // 添加底图
    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(map)

    // 添加缩放控制器到右下角
    L.control
      .zoom({
        position: "bottomright",
      })
      .addTo(map)

    // 创建方形网格热力图
    const createGridHeatmap = () => {
      const gridSize = 1.5 // 网格大小

      // 模拟中国海域的热力图数据
      const heatmapData = [
        // 渤海湾
        { lat: 39, lng: 118, intensity: 0.8, size: 3 },
        { lat: 38, lng: 120, intensity: 0.7, size: 2 },

        // 黄海
        { lat: 35, lng: 120, intensity: 0.9, size: 4 },
        { lat: 33, lng: 121, intensity: 0.8, size: 3 },

        // 东海
        { lat: 30, lng: 122, intensity: 0.9, size: 4 },
        { lat: 28, lng: 121, intensity: 0.7, size: 3 },
        { lat: 26, lng: 120, intensity: 0.6, size: 2 },

        // 台湾海峡
        { lat: 24, lng: 118, intensity: 0.8, size: 3 },
        { lat: 23, lng: 117, intensity: 0.7, size: 2 },

        // 南海
        { lat: 20, lng: 112, intensity: 0.9, size: 4 },
        { lat: 18, lng: 110, intensity: 0.8, size: 3 },
        { lat: 16, lng: 112, intensity: 0.7, size: 3 },
        { lat: 14, lng: 113, intensity: 0.6, size: 2 },

        // 珠江口
        { lat: 22, lng: 114, intensity: 0.8, size: 3 },

        // 长江口
        { lat: 31, lng: 121, intensity: 0.9, size: 4 },
      ]

      heatmapData.forEach((hotspot) => {
        const gridRadius = hotspot.size

        for (let latOffset = -gridRadius; latOffset <= gridRadius; latOffset++) {
          for (let lngOffset = -gridRadius; lngOffset <= gridRadius; lngOffset++) {
            const distance = Math.sqrt(latOffset * latOffset + lngOffset * lngOffset)
            if (distance <= gridRadius) {
              const cellLat = hotspot.lat + latOffset * gridSize
              const cellLng = hotspot.lng + lngOffset * gridSize

              const distanceRatio = 1 - distance / gridRadius
              // 使用位置坐标作为种子生成固定的伪随机值，避免Math.random()
              const seed = Math.abs(Math.floor(cellLat * 1000 + cellLng * 1000)) % 1000
              const pseudoRandom = (seed * 9301 + 49297) % 233280 / 233280
              const cellIntensity = hotspot.intensity * distanceRatio * (0.4 + pseudoRandom * 0.6)

              if (cellIntensity > 0.15) {
                const bounds = [
                  [cellLat - gridSize / 2, cellLng - gridSize / 2],
                  [cellLat + gridSize / 2, cellLng + gridSize / 2],
                ] as [[number, number], [number, number]]

                let color = "#06b6d4"
                if (cellIntensity > 0.8) color = "#dc2626"
                else if (cellIntensity > 0.7) color = "#ea580c"
                else if (cellIntensity > 0.6) color = "#f97316"
                else if (cellIntensity > 0.5) color = "#f59e0b"
                else if (cellIntensity > 0.4) color = "#eab308"
                else if (cellIntensity > 0.3) color = "#84cc16"
                else if (cellIntensity > 0.2) color = "#22c55e"

                L.rectangle(bounds, {
                  color: color,
                  fillColor: color,
                  fillOpacity: Math.min(cellIntensity * 0.7, 0.8),
                  weight: 0,
                  stroke: false,
                })
                  .addTo(map)
                  .bindPopup(`
                  <div class="p-2">
                    <h3 class="font-semibold text-sm mb-2">海洋生物热点 #${seed}</h3>
                    <p class="text-xs text-gray-600 mb-1">物种数量: ${Math.floor(cellIntensity * 150)}种</p>
                    <p class="text-xs text-gray-600 mb-1">录音数量: ${Math.floor(cellIntensity * 800)}条</p>
                    <p class="text-xs text-gray-600">坐标: ${cellLat.toFixed(2)}°N, ${cellLng.toFixed(2)}°E</p>
                  </div>
                `)
              }
            }
          }
        }
      })
    }

    createGridHeatmap()

    // 添加采集点标记（保持原有的圆形标记）
    const collectionPoints = [
      { lat: 39.0, lng: 118.0, species: "渤海湾", count: 89 },
      { lat: 31.0, lng: 121.5, species: "长江口", count: 156 },
      { lat: 22.3, lng: 114.2, species: "珠江口", count: 134 },
      { lat: 20.0, lng: 110.0, species: "南海北部", count: 203 },
      { lat: 24.0, lng: 118.0, species: "台湾海峡", count: 167 },
      { lat: 35.0, lng: 120.0, species: "黄海中部", count: 145 },
    ]

    // 使用marker层管理器添加采集点
    if (markerManager) {
      const collectionMarkers: MarkerLayerConfig = {
        id: "collection-points",
        name: "海洋生物采集点",
        visible: true,
        color: "#3388ff",
        icon: markerIcons.collection,
        markers: collectionPoints.map((point, index) => ({
          id: `collection-${index}`,
          lat: point.lat,
          lng: point.lng,
          title: `${point.species}采集点`,
          description: `物种数量: ${point.count}种，录音数量: ${Math.floor(point.count * 3.2)}条`,
          category: "采集点",
          popupContent: `
            <div class="p-3 min-w-48">
              <h3 class="font-semibold text-sm mb-2">${point.species}采集点</h3>
              <div class="space-y-1 text-xs text-gray-600">
                <p>物种数量: <span class="font-medium">${point.count}种</span></p>
                <p>录音数量: <span class="font-medium">${Math.floor(point.count * 3.2)}条</span></p>
                <p>坐标: <span class="font-mono">${point.lat.toFixed(2)}°N, ${point.lng.toFixed(2)}°E</span></p>
                <p>最近更新: <span class="font-medium">2024-01-15</span></p>
              </div>
              <div class="mt-3 flex gap-2">
                <button class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors">
                  查看详情
                </button>
                <button class="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600 transition-colors">
                  播放录音
                </button>
              </div>
            </div>
          `
        }))
      }
      
      markerManager.addMarkerLayer(collectionMarkers)
      setShowLayerControl(true)
    }

    mapInstanceRef.current = map

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [])

  return (
    <div className="relative w-full h-full">
      <div 
        ref={mapRef} 
        className="w-full h-full min-h-[400px] lg:min-h-[600px]" 
        style={{ 
          height: "100%",
        }} 
      />
      
      {/* 图层控制面板 */}
      {showLayerControl && markerManager && (
        <div className="absolute top-4 right-4 z-[1000]">
          <LayerControlPanel 
            markerManager={markerManager}
            compact={true}
            className="w-64"
          />
        </div>
      )}
    </div>
  )
}
