"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import MainLayout from "@/components/layout/main-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Search, Filter, ChevronDown, Play, Heart, Download, MapPin, Calendar, Volume2 } from "lucide-react"
import Image from "next/image"

interface Species {
  id: string
  name: string
  scientificName: string
  category: string
  habitat: string
  recordingCount: number
  image: string
  description: string
  lastUpdated: string
}

export default function SpeciesListPage() {
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(searchParams?.get("q") || "")
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedHabitats, setSelectedHabitats] = useState<string[]>([])
  const [sortBy, setSortBy] = useState("name")
  const [currentPage, setCurrentPage] = useState(1)
  const [species, setSpecies] = useState<Species[]>([])
  const [loading, setLoading] = useState(true)
  const [totalCount, setTotalCount] = useState(0)

  const itemsPerPage = 12

  // 模拟物种数据
  useEffect(() => {
    const mockSpecies: Species[] = [
      {
        id: "1",
        name: "蓝鲸",
        scientificName: "Balaenoptera musculus",
        category: "哺乳动物",
        habitat: "深海",
        recordingCount: 156,
        image: "/placeholder.svg?height=150&width=150",
        description: "世界上最大的动物，以其低频歌声闻名",
        lastUpdated: "2024-01-15",
      },
      {
        id: "2",
        name: "座头鲸",
        scientificName: "Megaptera novaeangliae",
        category: "哺乳动物",
        habitat: "深海",
        recordingCount: 234,
        image: "/placeholder.svg?height=150&width=150",
        description: "以复杂的歌声和长距离迁徙著称",
        lastUpdated: "2024-01-14",
      },
      {
        id: "3",
        name: "宽吻海豚",
        scientificName: "Tursiops truncatus",
        category: "哺乳动物",
        habitat: "浅海",
        recordingCount: 89,
        image: "/placeholder.svg?height=150&width=150",
        description: "智能的海洋哺乳动物，具有复杂的声音交流系统",
        lastUpdated: "2024-01-13",
      },
      {
        id: "4",
        name: "小丑鱼",
        scientificName: "Amphiprioninae",
        category: "鱼类",
        habitat: "珊瑚礁",
        recordingCount: 45,
        image: "/placeholder.svg?height=150&width=150",
        description: "生活在海葵中的色彩鲜艳的鱼类",
        lastUpdated: "2024-01-12",
      },
      {
        id: "5",
        name: "虎鲸",
        scientificName: "Orcinus orca",
        category: "哺乳动物",
        habitat: "深海",
        recordingCount: 178,
        image: "/placeholder.svg?height=150&width=150",
        description: "海洋中的顶级掠食者，具有复杂的社会结构",
        lastUpdated: "2024-01-11",
      },
      {
        id: "6",
        name: "海马",
        scientificName: "Hippocampus",
        category: "鱼类",
        habitat: "浅海",
        recordingCount: 23,
        image: "/placeholder.svg?height=150&width=150",
        description: "独特的海洋生物，雄性负责孵化幼体",
        lastUpdated: "2024-01-10",
      },
    ]

    // 模拟加载延迟
    setTimeout(() => {
      setSpecies(mockSpecies)
      setTotalCount(mockSpecies.length)
      setLoading(false)
    }, 1000)
  }, [])

  const categories = ["哺乳动物", "鱼类", "甲壳动物", "软体动物", "其他"]
  const habitats = ["深海", "浅海", "珊瑚礁", "河口", "极地海域"]

  const filteredSpecies = species.filter((s) => {
    const matchesSearch =
      !searchQuery ||
      s.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      s.scientificName.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = selectedCategories.length === 0 || selectedCategories.includes(s.category)

    const matchesHabitat = selectedHabitats.length === 0 || selectedHabitats.includes(s.habitat)

    return matchesSearch && matchesCategory && matchesHabitat
  })

  const totalPages = Math.ceil(filteredSpecies.length / itemsPerPage)
  const currentSpecies = filteredSpecies.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2 text-sm">
            <li>
              <a href="/" className="text-blue-600 hover:text-blue-800">
                首页
              </a>
            </li>
            <li className="text-gray-500">/</li>
            <li className="text-gray-900">数据搜索</li>
          </ol>
        </nav>

        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold marine-primary mb-4">物种搜索</h1>

          {/* Main Search */}
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="搜索物种名称、分类或特征..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-3 text-lg"
              />
            </div>
            <Button size="lg" className="px-8">
              <Search className="w-4 h-4 mr-2" />
              搜索
            </Button>
          </div>

          {/* Advanced Filters */}
          <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
            <CollapsibleTrigger asChild>
              <Button variant="outline" className="mb-4 bg-transparent">
                <Filter className="w-4 h-4 mr-2" />
                高级筛选
                <ChevronDown className={`w-4 h-4 ml-2 transition-transform ${isAdvancedOpen ? "rotate-180" : ""}`} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <Card className="mb-6">
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2">分类筛选</label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择分类" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部分类</SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">栖息环境</label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择环境" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部环境</SelectItem>
                          {habitats.map((habitat) => (
                            <SelectItem key={habitat} value={habitat}>
                              {habitat}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">排序方式</label>
                      <Select value={sortBy} onValueChange={setSortBy}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="name">按名称排序</SelectItem>
                          <SelectItem value="recordings">按录音数量</SelectItem>
                          <SelectItem value="updated">按更新时间</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CollapsibleContent>
          </Collapsible>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left Sidebar Filters */}
          <div className="w-full lg:w-64 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">分类筛选</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {categories.map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={category}
                        checked={selectedCategories.includes(category)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedCategories([...selectedCategories, category])
                          } else {
                            setSelectedCategories(selectedCategories.filter((c) => c !== category))
                          }
                        }}
                      />
                      <label htmlFor={category} className="text-sm">
                        {category}
                        <Badge variant="secondary" className="ml-2">
                          {species.filter((s) => s.category === category).length}
                        </Badge>
                      </label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">栖息环境</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {habitats.map((habitat) => (
                    <div key={habitat} className="flex items-center space-x-2">
                      <Checkbox
                        id={habitat}
                        checked={selectedHabitats.includes(habitat)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedHabitats([...selectedHabitats, habitat])
                          } else {
                            setSelectedHabitats(selectedHabitats.filter((h) => h !== habitat))
                          }
                        }}
                      />
                      <label htmlFor={habitat} className="text-sm">
                        {habitat}
                        <Badge variant="secondary" className="ml-2">
                          {species.filter((s) => s.habitat === habitat).length}
                        </Badge>
                      </label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold">
                  搜索结果
                  <Badge variant="secondary" className="ml-2">
                    {filteredSpecies.length} 个结果
                  </Badge>
                </h2>
              </div>
            </div>

            {/* Species Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <div className="h-40 bg-gray-200 rounded-t-lg"></div>
                    <CardContent className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {currentSpecies.map((species) => (
                  <Card key={species.id} className="species-card cursor-pointer group">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Image
                        src={species.image || "/placeholder.svg"}
                        alt={species.name}
                        width={300}
                        height={200}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-2 right-2 flex space-x-1">
                        <Button size="icon" variant="secondary" className="w-8 h-8 bg-white/80 hover:bg-white">
                          <Heart className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="absolute bottom-2 left-2">
                        <Badge className="bg-black/70 text-white">
                          <Volume2 className="w-3 h-3 mr-1" />
                          {species.recordingCount}
                        </Badge>
                      </div>
                    </div>

                    <CardContent className="p-4">
                      <div className="mb-2">
                        <h3 className="font-semibold text-lg marine-primary">{species.name}</h3>
                        <p className="text-sm text-gray-600 italic">{species.scientificName}</p>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-3">
                        <Badge variant="outline">{species.category}</Badge>
                        <Badge variant="outline">{species.habitat}</Badge>
                      </div>

                      <p className="text-sm text-gray-600 mb-4 line-clamp-2">{species.description}</p>

                      <div className="flex justify-between items-center">
                        <Button size="sm" onClick={() => (window.location.href = `/species/${species.id}`)}>
                          查看详情
                        </Button>
                        <div className="flex space-x-1">
                          <Button size="icon" variant="ghost" className="w-8 h-8">
                            <Play className="w-4 h-4" />
                          </Button>
                          <Button size="icon" variant="ghost" className="w-8 h-8">
                            <Download className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-3 pt-3 border-t text-xs text-gray-500">
                        <span className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {species.lastUpdated}
                        </span>
                        <span className="flex items-center">
                          <MapPin className="w-3 h-3 mr-1" />
                          全球分布
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination */}
            {!loading && totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(currentPage - 1)}
                  >
                    上一页
                  </Button>

                  {[...Array(totalPages)].map((_, i) => (
                    <Button
                      key={i + 1}
                      variant={currentPage === i + 1 ? "default" : "outline"}
                      onClick={() => setCurrentPage(i + 1)}
                    >
                      {i + 1}
                    </Button>
                  ))}

                  <Button
                    variant="outline"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(currentPage + 1)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
