"use client"

import { useRef, useEffect } from "react"
import L from "leaflet"

// 定义marker数据类型
export interface MarkerData {
  id: string
  lat: number
  lng: number
  title: string
  description?: string
  category?: string
  icon?: L.Icon
  popupContent?: string
}

// 定义marker层配置
export interface MarkerLayerConfig {
  id: string
  name: string
  visible: boolean
  markers: MarkerData[]
  icon?: L.Icon
  color?: string
}

// 创建默认标记图标
const createDefaultIcon = (color: string = "#3388ff") => {
  return L.icon({
    iconUrl: 'data:image/svg+xml;base64,' + btoa(`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 41" width="25" height="41">
        <path fill="${color}" stroke="#fff" stroke-width="2" d="M12.5 0C5.6 0 0 5.6 0 12.5c0 4.7 2.6 8.8 6.5 11l6 17.5 6-17.5c3.9-2.2 6.5-6.3 6.5-11C25 5.6 19.4 0 12.5 0z"/>
        <circle fill="#fff" cx="12.5" cy="12.5" r="5"/>
      </svg>
    `),
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowUrl: undefined,
    shadowSize: undefined,
    shadowAnchor: undefined
  })
}

// 预定义不同类型的marker图标
export const markerIcons = {
  collection: createDefaultIcon("#3388ff"), // 蓝色 - 采集点
  observation: createDefaultIcon("#22c55e"), // 绿色 - 观测点
  research: createDefaultIcon("#f59e0b"), // 橙色 - 科研站
  monitoring: createDefaultIcon("#ef4444"), // 红色 - 监测点
  default: createDefaultIcon("#6b7280"), // 灰色 - 默认
}

export class MarkerLayerManager {
  private map: L.Map
  private layerGroups: Map<string, L.LayerGroup> = new Map()
  private markerLayers: Map<string, MarkerLayerConfig> = new Map()
  private allMarkersGroup: L.LayerGroup

  constructor(map: L.Map) {
    this.map = map
    this.allMarkersGroup = L.layerGroup()
  }

  /**
   * 添加marker层
   */
  addMarkerLayer(config: MarkerLayerConfig): void {
    const layerGroup = L.layerGroup()
    
    // 创建markers
    config.markers.forEach(markerData => {
      const icon = markerData.icon || config.icon || markerIcons.default
      const marker = L.marker([markerData.lat, markerData.lng], { icon })
      
      // 添加弹窗
      if (markerData.popupContent) {
        marker.bindPopup(markerData.popupContent)
      } else {
        marker.bindPopup(`
          <div class="p-3 min-w-48">
            <h4 class="font-semibold mb-2">${markerData.title}</h4>
            ${markerData.description ? `<p class="text-sm text-gray-600 mb-2">${markerData.description}</p>` : ''}
            <div class="text-xs text-gray-500">
              <p>坐标: ${markerData.lat.toFixed(4)}°N, ${markerData.lng.toFixed(4)}°E</p>
              ${markerData.category ? `<p>类别: ${markerData.category}</p>` : ''}
            </div>
          </div>
        `)
      }
      
      layerGroup.addLayer(marker)
    })

    // 存储层配置和图层组
    this.markerLayers.set(config.id, config)
    this.layerGroups.set(config.id, layerGroup)

    // 如果设置为可见，则添加到地图
    if (config.visible) {
      this.showLayer(config.id)
    }
  }

  /**
   * 显示marker层
   */
  showLayer(layerId: string): void {
    const layerGroup = this.layerGroups.get(layerId)
    const config = this.markerLayers.get(layerId)
    
    if (layerGroup && config) {
      layerGroup.addTo(this.map)
      config.visible = true
    }
  }

  /**
   * 隐藏marker层
   */
  hideLayer(layerId: string): void {
    const layerGroup = this.layerGroups.get(layerId)
    const config = this.markerLayers.get(layerId)
    
    if (layerGroup && config) {
      this.map.removeLayer(layerGroup)
      config.visible = false
    }
  }

  /**
   * 切换marker层显示状态
   */
  toggleLayer(layerId: string): boolean {
    const config = this.markerLayers.get(layerId)
    if (config) {
      if (config.visible) {
        this.hideLayer(layerId)
      } else {
        this.showLayer(layerId)
      }
      return config.visible
    }
    return false
  }

  /**
   * 显示所有marker层
   */
  showAllLayers(): void {
    this.markerLayers.forEach((_, layerId) => {
      this.showLayer(layerId)
    })
  }

  /**
   * 隐藏所有marker层
   */
  hideAllLayers(): void {
    this.markerLayers.forEach((_, layerId) => {
      this.hideLayer(layerId)
    })
  }

  /**
   * 获取层的可见状态
   */
  isLayerVisible(layerId: string): boolean {
    const config = this.markerLayers.get(layerId)
    return config?.visible || false
  }

  /**
   * 获取所有层的配置
   */
  getAllLayers(): MarkerLayerConfig[] {
    return Array.from(this.markerLayers.values())
  }

  /**
   * 移除marker层
   */
  removeLayer(layerId: string): void {
    const layerGroup = this.layerGroups.get(layerId)
    if (layerGroup) {
      this.map.removeLayer(layerGroup)
      this.layerGroups.delete(layerId)
      this.markerLayers.delete(layerId)
    }
  }

  /**
   * 更新marker层数据
   */
  updateLayer(layerId: string, newConfig: Partial<MarkerLayerConfig>): void {
    const currentConfig = this.markerLayers.get(layerId)
    if (currentConfig) {
      // 移除旧层
      this.removeLayer(layerId)
      
      // 创建新层配置
      const updatedConfig: MarkerLayerConfig = {
        ...currentConfig,
        ...newConfig,
        id: layerId, // 确保ID不变
      }
      
      // 添加更新后的层
      this.addMarkerLayer(updatedConfig)
    }
  }

  /**
   * 清空所有marker层
   */
  clearAllLayers(): void {
    this.layerGroups.forEach((layerGroup) => {
      this.map.removeLayer(layerGroup)
    })
    this.layerGroups.clear()
    this.markerLayers.clear()
  }

  /**
   * 获取指定层的marker数量
   */
  getLayerMarkerCount(layerId: string): number {
    const config = this.markerLayers.get(layerId)
    return config?.markers.length || 0
  }

  /**
   * 获取所有可见marker的总数
   */
  getVisibleMarkerCount(): number {
    let count = 0
    this.markerLayers.forEach((config) => {
      if (config.visible) {
        count += config.markers.length
      }
    })
    return count
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.clearAllLayers()
  }
}

// React Hook for using MarkerLayerManager
export function useMarkerLayerManager(map: L.Map | null) {
  const managerRef = useRef<MarkerLayerManager | null>(null)

  useEffect(() => {
    if (map && !managerRef.current) {
      managerRef.current = new MarkerLayerManager(map)
    }

    return () => {
      if (managerRef.current) {
        managerRef.current.destroy()
        managerRef.current = null
      }
    }
  }, [map])

  return managerRef.current
} 