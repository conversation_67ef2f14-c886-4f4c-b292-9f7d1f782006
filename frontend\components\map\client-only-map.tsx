'use client'

import { useEffect, useState } from 'react'

interface ClientOnlyMapProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ClientOnlyMap({ children, fallback = null }: ClientOnlyMapProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return <>{fallback}</>
  }

  return <>{children}</>
} 