"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d";
exports.ids = ["vendor-chunks/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d/node_modules/@radix-ui/react-select/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d/node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.0/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_5a9538d81cc9a82cfec21341eafe5f60/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_033f2d9864c311ebc46e3d65a72ec4b7/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_e0654b1a402476d2bc9d17a4916b81c5/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_269ed620171cebd49025512d22fad1ff/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f4aa807b3e1605c991a664dd895fa0ef/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._eb5128f7adaf3128c1076c6b6e93c13d/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_2dc63ba6354ec7ef7d955ba47145829a/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_84b05e8d8acde331bf79519153011e46/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_7d9c308966eafc0f645092b629b133a3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_f62eb26ebf07111fa37167228db23a06/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_da21897b5bc909f873add44d7cbc4eba/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.8_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // packages/react/select/src/Select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                            \"Select.useCallback\": (option)=>{\n                                setNativeOptionsSet({\n                                    \"Select.useCallback\": (prev)=>new Set(prev).add(option)\n                                }[\"Select.useCallback\"]);\n                            }\n                        }[\"Select.useCallback\"], []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                            \"Select.useCallback\": (option)=>{\n                                setNativeOptionsSet({\n                                    \"Select.useCallback\": (prev)=>{\n                                        const optionsSet = new Set(prev);\n                                        optionsSet.delete(option);\n                                        return optionsSet;\n                                    }\n                                }[\"Select.useCallback\"]);\n                            }\n                        }[\"Select.useCallback\"], []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(BubbleSelect, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch({\n        \"SelectTrigger.useTypeaheadSearch\": (search)=>{\n            const enabledItems = getItems().filter({\n                \"SelectTrigger.useTypeaheadSearch.enabledItems\": (item)=>!item.disabled\n            }[\"SelectTrigger.useTypeaheadSearch.enabledItems\"]);\n            const currentItem = enabledItems.find({\n                \"SelectTrigger.useTypeaheadSearch.currentItem\": (item)=>item.value === context.value\n            }[\"SelectTrigger.useTypeaheadSearch.currentItem\"]);\n            const nextItem = findNextItem(enabledItems, search, currentItem);\n            if (nextItem !== void 0) {\n                context.onValueChange(nextItem.value);\n            }\n        }\n    }[\"SelectTrigger.useTypeaheadSearch\"]);\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectValue.useLayoutEffect\": ()=>{\n            onValueNodeHasChildrenChange(hasChildren);\n        }\n    }[\"SelectValue.useLayoutEffect\"], [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"\\u25BC\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectContent.useLayoutEffect\": ()=>{\n            setFragment(new DocumentFragment());\n        }\n    }[\"SelectContent.useLayoutEffect\"], []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectContentImpl.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"SelectContentImpl.useComposedRefs[composedRefs]\"]);\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_14__.hideOthers)(content);\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[focusFirst]\": (candidates)=>{\n            const [firstItem, ...restItems] = getItems().map({\n                \"SelectContentImpl.useCallback[focusFirst]\": (item)=>item.ref.current\n            }[\"SelectContentImpl.useCallback[focusFirst]\"]);\n            const [lastItem] = restItems.slice(-1);\n            const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n            for (const candidate of candidates){\n                if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n                candidate?.scrollIntoView({\n                    block: \"nearest\"\n                });\n                if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n                if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n                candidate?.focus();\n                if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n            }\n        }\n    }[\"SelectContentImpl.useCallback[focusFirst]\"], [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[focusSelectedItem]\": ()=>focusFirst([\n                selectedItem,\n                content\n            ])\n    }[\"SelectContentImpl.useCallback[focusSelectedItem]\"], [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (isPositioned) {\n                focusSelectedItem();\n            }\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (content) {\n                let pointerMoveDelta = {\n                    x: 0,\n                    y: 0\n                };\n                const handlePointerMove = {\n                    \"SelectContentImpl.useEffect.handlePointerMove\": (event)=>{\n                        pointerMoveDelta = {\n                            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                        };\n                    }\n                }[\"SelectContentImpl.useEffect.handlePointerMove\"];\n                const handlePointerUp = {\n                    \"SelectContentImpl.useEffect.handlePointerUp\": (event)=>{\n                        if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                            event.preventDefault();\n                        } else {\n                            if (!content.contains(event.target)) {\n                                onOpenChange(false);\n                            }\n                        }\n                        document.removeEventListener(\"pointermove\", handlePointerMove);\n                        triggerPointerDownPosRef.current = null;\n                    }\n                }[\"SelectContentImpl.useEffect.handlePointerUp\"];\n                if (triggerPointerDownPosRef.current !== null) {\n                    document.addEventListener(\"pointermove\", handlePointerMove);\n                    document.addEventListener(\"pointerup\", handlePointerUp, {\n                        capture: true,\n                        once: true\n                    });\n                }\n                return ({\n                    \"SelectContentImpl.useEffect\": ()=>{\n                        document.removeEventListener(\"pointermove\", handlePointerMove);\n                        document.removeEventListener(\"pointerup\", handlePointerUp, {\n                            capture: true\n                        });\n                    }\n                })[\"SelectContentImpl.useEffect\"];\n            }\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            const close = {\n                \"SelectContentImpl.useEffect.close\": ()=>onOpenChange(false)\n            }[\"SelectContentImpl.useEffect.close\"];\n            window.addEventListener(\"blur\", close);\n            window.addEventListener(\"resize\", close);\n            return ({\n                \"SelectContentImpl.useEffect\": ()=>{\n                    window.removeEventListener(\"blur\", close);\n                    window.removeEventListener(\"resize\", close);\n                }\n            })[\"SelectContentImpl.useEffect\"];\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch({\n        \"SelectContentImpl.useTypeaheadSearch\": (search)=>{\n            const enabledItems = getItems().filter({\n                \"SelectContentImpl.useTypeaheadSearch.enabledItems\": (item)=>!item.disabled\n            }[\"SelectContentImpl.useTypeaheadSearch.enabledItems\"]);\n            const currentItem = enabledItems.find({\n                \"SelectContentImpl.useTypeaheadSearch.currentItem\": (item)=>item.ref.current === document.activeElement\n            }[\"SelectContentImpl.useTypeaheadSearch.currentItem\"]);\n            const nextItem = findNextItem(enabledItems, search, currentItem);\n            if (nextItem) {\n                setTimeout({\n                    \"SelectContentImpl.useTypeaheadSearch\": ()=>nextItem.ref.current.focus()\n                }[\"SelectContentImpl.useTypeaheadSearch\"]);\n            }\n        }\n    }[\"SelectContentImpl.useTypeaheadSearch\"]);\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[itemRefCallback]\": (node, value, disabled)=>{\n            const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n            const isSelectedItem = context.value !== void 0 && context.value === value;\n            if (isSelectedItem || isFirstValidItem) {\n                setSelectedItem(node);\n                if (isFirstValidItem) firstValidItemFoundRef.current = true;\n            }\n        }\n    }[\"SelectContentImpl.useCallback[itemRefCallback]\"], [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[handleItemLeave]\": ()=>content?.focus()\n    }[\"SelectContentImpl.useCallback[handleItemLeave]\"], [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[itemTextRefCallback]\": (node, value, disabled)=>{\n            const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n            const isSelectedItem = context.value !== void 0 && context.value === value;\n            if (isSelectedItem || isFirstValidItem) {\n                setSelectedItemText(node);\n            }\n        }\n    }[\"SelectContentImpl.useCallback[itemTextRefCallback]\"], [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__.Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItemAlignedPosition.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"SelectItemAlignedPosition.useComposedRefs[composedRefs]\"]);\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectItemAlignedPosition.useCallback[position]\": ()=>{\n            if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n                const triggerRect = context.trigger.getBoundingClientRect();\n                const contentRect = content.getBoundingClientRect();\n                const valueNodeRect = context.valueNode.getBoundingClientRect();\n                const itemTextRect = selectedItemText.getBoundingClientRect();\n                if (context.dir !== \"rtl\") {\n                    const itemTextOffset = itemTextRect.left - contentRect.left;\n                    const left = valueNodeRect.left - itemTextOffset;\n                    const leftDelta = triggerRect.left - left;\n                    const minContentWidth = triggerRect.width + leftDelta;\n                    const contentWidth = Math.max(minContentWidth, contentRect.width);\n                    const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                    const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                        CONTENT_MARGIN,\n                        // Prevents the content from going off the starting edge of the\n                        // viewport. It may still go off the ending edge, but this can be\n                        // controlled by the user since they may want to manage overflow in a\n                        // specific way.\n                        // https://github.com/radix-ui/primitives/issues/2049\n                        Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                    ]);\n                    contentWrapper.style.minWidth = minContentWidth + \"px\";\n                    contentWrapper.style.left = clampedLeft + \"px\";\n                } else {\n                    const itemTextOffset = contentRect.right - itemTextRect.right;\n                    const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                    const rightDelta = window.innerWidth - triggerRect.right - right;\n                    const minContentWidth = triggerRect.width + rightDelta;\n                    const contentWidth = Math.max(minContentWidth, contentRect.width);\n                    const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                    const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                        CONTENT_MARGIN,\n                        Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                    ]);\n                    contentWrapper.style.minWidth = minContentWidth + \"px\";\n                    contentWrapper.style.right = clampedRight + \"px\";\n                }\n                const items = getItems();\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const itemsHeight = viewport.scrollHeight;\n                const contentStyles = window.getComputedStyle(content);\n                const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n                const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n                const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n                const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n                const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n                const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n                const viewportStyles = window.getComputedStyle(viewport);\n                const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n                const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n                const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n                const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n                const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n                const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n                const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n                const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n                const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n                if (willAlignWithoutTopOverflow) {\n                    const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                    contentWrapper.style.bottom = \"0px\";\n                    const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                    const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                    (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                    const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                    contentWrapper.style.height = height + \"px\";\n                } else {\n                    const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                    contentWrapper.style.top = \"0px\";\n                    const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                    (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                    const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                    contentWrapper.style.height = height + \"px\";\n                    viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n                }\n                contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n                contentWrapper.style.minHeight = minContentHeight + \"px\";\n                contentWrapper.style.maxHeight = availableHeight + \"px\";\n                onPlaced?.();\n                requestAnimationFrame({\n                    \"SelectItemAlignedPosition.useCallback[position]\": ()=>shouldExpandOnScrollRef.current = true\n                }[\"SelectItemAlignedPosition.useCallback[position]\"]);\n            }\n        }\n    }[\"SelectItemAlignedPosition.useCallback[position]\"], [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemAlignedPosition.useLayoutEffect\": ()=>position()\n    }[\"SelectItemAlignedPosition.useLayoutEffect\"], [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemAlignedPosition.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"SelectItemAlignedPosition.useLayoutEffect\"], [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectItemAlignedPosition.useCallback[handleScrollButtonChange]\": (node)=>{\n            if (node && shouldRepositionRef.current === true) {\n                position();\n                focusSelectedItem?.();\n                shouldRepositionRef.current = false;\n            }\n        }\n    }[\"SelectItemAlignedPosition.useCallback[handleScrollButtonChange]\"], [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItem.useComposedRefs[composedRefs]\": (node)=>contentContext.itemRefCallback?.(node, value, disabled)\n    }[\"SelectItem.useComposedRefs[composedRefs]\"]);\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"SelectItem.useCallback\": (node)=>{\n                setTextValue({\n                    \"SelectItem.useCallback\": (prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim()\n                }[\"SelectItem.useCallback\"]);\n            }\n        }[\"SelectItem.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItemText.useComposedRefs[composedRefs]\": (node)=>setItemTextNode(node)\n    }[\"SelectItemText.useComposedRefs[composedRefs]\"], itemContext.onItemTextChange, {\n        \"SelectItemText.useComposedRefs[composedRefs]\": (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    }[\"SelectItemText.useComposedRefs[composedRefs]\"]);\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"SelectItemText.useMemo[nativeOption]\": ()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                value: itemContext.value,\n                disabled: itemContext.disabled,\n                children: textContent\n            }, itemContext.value)\n    }[\"SelectItemText.useMemo[nativeOption]\"], [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemText.useLayoutEffect\": ()=>{\n            onNativeOptionAdd(nativeOption);\n            return ({\n                \"SelectItemText.useLayoutEffect\": ()=>onNativeOptionRemove(nativeOption)\n            })[\"SelectItemText.useLayoutEffect\"];\n        }\n    }[\"SelectItemText.useLayoutEffect\"], [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollUpButton.useLayoutEffect\": ()=>{\n            if (contentContext.viewport && contentContext.isPositioned) {\n                let handleScroll2 = {\n                    \"SelectScrollUpButton.useLayoutEffect.handleScroll2\": function() {\n                        const canScrollUp2 = viewport.scrollTop > 0;\n                        setCanScrollUp(canScrollUp2);\n                    }\n                }[\"SelectScrollUpButton.useLayoutEffect.handleScroll2\"];\n                var handleScroll = handleScroll2;\n                const viewport = contentContext.viewport;\n                handleScroll2();\n                viewport.addEventListener(\"scroll\", handleScroll2);\n                return ({\n                    \"SelectScrollUpButton.useLayoutEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll2)\n                })[\"SelectScrollUpButton.useLayoutEffect\"];\n            }\n        }\n    }[\"SelectScrollUpButton.useLayoutEffect\"], [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollDownButton.useLayoutEffect\": ()=>{\n            if (contentContext.viewport && contentContext.isPositioned) {\n                let handleScroll2 = {\n                    \"SelectScrollDownButton.useLayoutEffect.handleScroll2\": function() {\n                        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                        setCanScrollDown(canScrollDown2);\n                    }\n                }[\"SelectScrollDownButton.useLayoutEffect.handleScroll2\"];\n                var handleScroll = handleScroll2;\n                const viewport = contentContext.viewport;\n                handleScroll2();\n                viewport.addEventListener(\"scroll\", handleScroll2);\n                return ({\n                    \"SelectScrollDownButton.useLayoutEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll2)\n                })[\"SelectScrollDownButton.useLayoutEffect\"];\n            }\n        }\n    }[\"SelectScrollDownButton.useLayoutEffect\"], [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectScrollButtonImpl.useCallback[clearAutoScrollTimer]\": ()=>{\n            if (autoScrollTimerRef.current !== null) {\n                window.clearInterval(autoScrollTimerRef.current);\n                autoScrollTimerRef.current = null;\n            }\n        }\n    }[\"SelectScrollButtonImpl.useCallback[clearAutoScrollTimer]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectScrollButtonImpl.useEffect\": ()=>{\n            return ({\n                \"SelectScrollButtonImpl.useEffect\": ()=>clearAutoScrollTimer()\n            })[\"SelectScrollButtonImpl.useEffect\"];\n        }\n    }[\"SelectScrollButtonImpl.useEffect\"], [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollButtonImpl.useLayoutEffect\": ()=>{\n            const activeItem = getItems().find({\n                \"SelectScrollButtonImpl.useLayoutEffect.activeItem\": (item)=>item.ref.current === document.activeElement\n            }[\"SelectScrollButtonImpl.useLayoutEffect.activeItem\"]);\n            activeItem?.ref.current?.scrollIntoView({\n                block: \"nearest\"\n            });\n        }\n    }[\"SelectScrollButtonImpl.useLayoutEffect\"], [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nvar BubbleSelect = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...selectProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleSelect.useEffect\": ()=>{\n            const select = ref.current;\n            const selectProto = window.HTMLSelectElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n            const setValue = descriptor.set;\n            if (prevValue !== value && setValue) {\n                const event = new Event(\"change\", {\n                    bubbles: true\n                });\n                setValue.call(select, value);\n                select.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleSelect.useEffect\"], [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VisuallyHidden, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"select\", {\n            ...selectProps,\n            ref: composedRefs,\n            defaultValue: value\n        })\n    });\n});\nBubbleSelect.displayName = \"BubbleSelect\";\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useTypeaheadSearch.useCallback[handleTypeaheadSearch]\": (key)=>{\n            const search = searchRef.current + key;\n            handleSearchChange(search);\n            (function updateSearch(value) {\n                searchRef.current = value;\n                window.clearTimeout(timerRef.current);\n                if (value !== \"\") timerRef.current = window.setTimeout({\n                    \"useTypeaheadSearch.useCallback[handleTypeaheadSearch].updateSearch\": ()=>updateSearch(\"\")\n                }[\"useTypeaheadSearch.useCallback[handleTypeaheadSearch].updateSearch\"], 1e3);\n            })(search);\n        }\n    }[\"useTypeaheadSearch.useCallback[handleTypeaheadSearch]\"], [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useTypeaheadSearch.useCallback[resetTypeahead]\": ()=>{\n            searchRef.current = \"\";\n            window.clearTimeout(timerRef.current);\n        }\n    }[\"useTypeaheadSearch.useCallback[resetTypeahead]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useTypeaheadSearch.useEffect\": ()=>{\n            return ({\n                \"useTypeaheadSearch.useEffect\": ()=>window.clearTimeout(timerRef.current)\n            })[\"useTypeaheadSearch.useEffect\"];\n        }\n    }[\"useTypeaheadSearch.useEffect\"], []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1._fc26463523a688c90e60aba080c6891d/node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ })

};
;