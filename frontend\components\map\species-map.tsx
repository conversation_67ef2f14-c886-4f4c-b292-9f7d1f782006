"use client"

import { useEffect, useRef } from "react"
import L from "leaflet"
import "leaflet/dist/leaflet.css"

interface SpeciesMapProps {
  speciesId: string
}

export default function SpeciesMap({ speciesId }: SpeciesMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<L.Map | null>(null)

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return

    // 初始化地图
    const map = L.map(mapRef.current, {
      center: [20.0, 0.0],
      zoom: 2,
      zoomControl: true,
      attributionControl: false,
    })

    // 添加底图
    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(map)

    // 模拟物种分布数据
    const distributionAreas = [
      { lat: 60, lng: -150, radius: 800000, name: "北太平洋" },
      { lat: -40, lng: 140, radius: 600000, name: "南太平洋" },
      { lat: 45, lng: -30, radius: 700000, name: "北大西洋" },
      { lat: -60, lng: 0, radius: 1000000, name: "南极海域" },
    ]

    // 添加分布区域
    distributionAreas.forEach((area) => {
      L.circle([area.lat, area.lng], {
        color: "#1e3a8a",
        fillColor: "#3b82f6",
        fillOpacity: 0.3,
        radius: area.radius,
      })
        .addTo(map)
        .bindPopup(`
        <div class="p-2">
          <h4 class="font-semibold">${area.name}</h4>
          <p class="text-sm text-gray-600">主要栖息区域</p>
        </div>
      `)
    })

    // 添加观测点
    const observationPoints = [
      { lat: 55, lng: -145, count: 23 },
      { lat: -35, lng: 145, count: 18 },
      { lat: 40, lng: -25, count: 31 },
      { lat: -55, lng: 5, count: 45 },
    ]

    observationPoints.forEach((point) => {
      L.marker([point.lat, point.lng])
        .addTo(map)
        .bindPopup(`
          <div class="p-2">
            <h4 class="font-semibold">观测点</h4>
            <p class="text-sm text-gray-600">记录数量: ${point.count}</p>
          </div>
        `)
    })

    mapInstanceRef.current = map

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [speciesId])

  return <div ref={mapRef} className="w-full h-64 rounded-lg" />
}
