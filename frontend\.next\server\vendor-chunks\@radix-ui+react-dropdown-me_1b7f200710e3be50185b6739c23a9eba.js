"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-dropdown-me_1b7f200710e3be50185b6739c23a9eba";
exports.ids = ["vendor-chunks/@radix-ui+react-dropdown-me_1b7f200710e3be50185b6739c23a9eba"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_1b7f200710e3be50185b6739c23a9eba/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dropdown-me_1b7f200710e3be50185b6739c23a9eba/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuArrow: () => (/* binding */ DropdownMenuArrow),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuItemIndicator: () => (/* binding */ DropdownMenuItemIndicator),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger),\n/* harmony export */   Group: () => (/* binding */ Group2),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator2),\n/* harmony export */   Label: () => (/* binding */ Label2),\n/* harmony export */   Portal: () => (/* binding */ Portal2),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup2),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem2),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Separator: () => (/* binding */ Separator2),\n/* harmony export */   Sub: () => (/* binding */ Sub2),\n/* harmony export */   SubContent: () => (/* binding */ SubContent2),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createDropdownMenuScope: () => (/* binding */ createDropdownMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_84b05e8d8acde331bf79519153011e46/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-menu */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.4__a1aa99958feec8b6e15aea7229a1de53/node_modules/@radix-ui/react-menu/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,CheckboxItem,Content,DropdownMenu,DropdownMenuArrow,DropdownMenuCheckboxItem,DropdownMenuContent,DropdownMenuGroup,DropdownMenuItem,DropdownMenuItemIndicator,DropdownMenuLabel,DropdownMenuPortal,DropdownMenuRadioGroup,DropdownMenuRadioItem,DropdownMenuSeparator,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuTrigger,Group,Item,ItemIndicator,Label,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,Trigger,createDropdownMenuScope auto */ // packages/react/dropdown-menu/src/DropdownMenu.tsx\n\n\n\n\n\n\n\n\n\n\nvar DROPDOWN_MENU_NAME = \"DropdownMenu\";\nvar [createDropdownMenuContext, createDropdownMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DROPDOWN_MENU_NAME, [\n    _radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.createMenuScope\n]);\nvar useMenuScope = (0,_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.createMenuScope)();\nvar [DropdownMenuProvider, useDropdownMenuContext] = createDropdownMenuContext(DROPDOWN_MENU_NAME);\nvar DropdownMenu = (props)=>{\n    const { __scopeDropdownMenu, children, dir, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DropdownMenuProvider, {\n        scope: __scopeDropdownMenu,\n        triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n        triggerRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"DropdownMenu.useCallback\": ()=>setOpen({\n                    \"DropdownMenu.useCallback\": (prevOpen)=>!prevOpen\n                }[\"DropdownMenu.useCallback\"])\n        }[\"DropdownMenu.useCallback\"], [\n            setOpen\n        ]),\n        modal,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Root, {\n            ...menuScope,\n            open,\n            onOpenChange: setOpen,\n            dir,\n            modal,\n            children\n        })\n    });\n};\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\nvar TRIGGER_NAME = \"DropdownMenuTrigger\";\nvar DropdownMenuTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...menuScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n            type: \"button\",\n            id: context.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": context.open ? context.contentId : void 0,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            ...triggerProps,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.composeRefs)(forwardedRef, context.triggerRef),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                    context.onOpenToggle();\n                    if (!context.open) event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (disabled) return;\n                if ([\n                    \"Enter\",\n                    \" \"\n                ].includes(event.key)) context.onOpenToggle();\n                if (event.key === \"ArrowDown\") context.onOpenChange(true);\n                if ([\n                    \"Enter\",\n                    \" \",\n                    \"ArrowDown\"\n                ].includes(event.key)) event.preventDefault();\n            })\n        })\n    });\n});\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DropdownMenuPortal\";\nvar DropdownMenuPortal = (props)=>{\n    const { __scopeDropdownMenu, ...portalProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        ...menuScope,\n        ...portalProps\n    });\n};\nDropdownMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"DropdownMenuContent\";\nvar DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        id: context.contentId,\n        \"aria-labelledby\": context.triggerId,\n        ...menuScope,\n        ...contentProps,\n        ref: forwardedRef,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            hasInteractedOutsideRef.current = false;\n            event.preventDefault();\n        }),\n        onInteractOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onInteractOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        }),\n        style: {\n            ...props.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nDropdownMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"DropdownMenuGroup\";\nvar DropdownMenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Group, {\n        ...menuScope,\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"DropdownMenuLabel\";\nvar DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ...menuScope,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"DropdownMenuItem\";\nvar DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ...menuScope,\n        ...itemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuItem.displayName = ITEM_NAME;\nvar CHECKBOX_ITEM_NAME = \"DropdownMenuCheckboxItem\";\nvar DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ...menuScope,\n        ...checkboxItemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"DropdownMenuRadioGroup\";\nvar DropdownMenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...radioGroupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n        ...menuScope,\n        ...radioGroupProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"DropdownMenuRadioItem\";\nvar DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...radioItemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ...menuScope,\n        ...radioItemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar INDICATOR_NAME = \"DropdownMenuItemIndicator\";\nvar DropdownMenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n        ...menuScope,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\nvar SEPARATOR_NAME = \"DropdownMenuSeparator\";\nvar DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...separatorProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ...menuScope,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"DropdownMenuArrow\";\nvar DropdownMenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...menuScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuArrow.displayName = ARROW_NAME;\nvar DropdownMenuSub = (props)=>{\n    const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Sub, {\n        ...menuScope,\n        open,\n        onOpenChange: setOpen,\n        children\n    });\n};\nvar SUB_TRIGGER_NAME = \"DropdownMenuSubTrigger\";\nvar DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...subTriggerProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ...menuScope,\n        ...subTriggerProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"DropdownMenuSubContent\";\nvar DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...subContentProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ...menuScope,\n        ...subContentProps,\n        ref: forwardedRef,\n        style: {\n            ...props.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\nvar Root2 = DropdownMenu;\nvar Trigger = DropdownMenuTrigger;\nvar Portal2 = DropdownMenuPortal;\nvar Content2 = DropdownMenuContent;\nvar Group2 = DropdownMenuGroup;\nvar Label2 = DropdownMenuLabel;\nvar Item2 = DropdownMenuItem;\nvar CheckboxItem2 = DropdownMenuCheckboxItem;\nvar RadioGroup2 = DropdownMenuRadioGroup;\nvar RadioItem2 = DropdownMenuRadioItem;\nvar ItemIndicator2 = DropdownMenuItemIndicator;\nvar Separator2 = DropdownMenuSeparator;\nvar Arrow2 = DropdownMenuArrow;\nvar Sub2 = DropdownMenuSub;\nvar SubTrigger2 = DropdownMenuSubTrigger;\nvar SubContent2 = DropdownMenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_1b7f200710e3be50185b6739c23a9eba/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\n");

/***/ })

};
;